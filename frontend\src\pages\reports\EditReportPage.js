import React from "react";
import { useParams } from "react-router-dom";
import EditReport from "../Companies/Components/EditReport";
import { getReportById } from "../../services/report";
const EditReportPage = () => {
  const { reportId } = useParams();
  const [reportDetail, setReportDetail] = React.useState(null);

  React.useEffect(() => {
    const fetchReport = async () => {
      try {
        const response = await getReportById(reportId);
        setReportDetail(response.data);
      } catch (error) {
        console.error("Error fetching report:", error);
      }
    };

    fetchReport();
  }, [reportId]);

  return (
    <div>
      {reportDetail ? (
        <EditReport report={reportDetail} setReportDetail={setReportDetail} />
      ) : (
        <div>Loading...</div>
      )}
    </div>
  );
};

export default EditReportPage;
