import axiosInstance from "./axiosInstance";

export const createRequest = async (data) => {
    return await axiosInstance.post("/report", data);
};

export const getAllCompanyReports = async (companyId) => {
    return await axiosInstance.get(`/report/${companyId}/all`);
}

export const deleteRequest = async(requestId) => {
    return await axiosInstance.delete(`/report/${requestId}`);
}

export const saveEditedReport = async(requestId, data) =>{
    return await axiosInstance.post(`/report/${requestId}/editable`, data)
}

export const getReportById = async(reportId) => {
    return await axiosInstance.get(`/report/${reportId}`)
}

export const saveEdits = async (reportId, payload) => {
    return await axiosInstance.post(`/report/${reportId}/save-text`, payload)
}