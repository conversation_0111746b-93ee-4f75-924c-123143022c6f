import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
import { customTemplate } from "../helpers/templateHelper.js";
const baseUrl = `/api/v1/templates`;

describe('GET /template/:id/download', () => {
    let templateId
    before(async()=>{
     templateId =  await customTemplate()
     
    })

    it('should download files successfully', async () => {
        const response = await request(app)
            .get(`${baseUrl}/${templateId}/download`)
            .expect(200);
        
        expect(response.headers['content-disposition']).contain('attachment');
        expect(response.headers['content-disposition']).contain('Template.zip');
    });


    it('should return 404 if no template found', async () => {
    
        const response = await request(app)
        .get(`${baseUrl}/01/download`)                

        expect(response._body.success).to.equal(false)
        expect(response._body.message).equal('Template not found')

    });



    it('should handle internal server error', async () => {

        const response = await request(app)
        .get(`${baseUrl}/${templateId}/download`)  
              
        expect(response.status).not.equal(500)
    });


});
