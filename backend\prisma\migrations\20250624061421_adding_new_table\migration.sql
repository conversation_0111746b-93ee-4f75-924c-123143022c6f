-- CreateEnum
CREATE TYPE "QboConnectionStatus" AS ENUM ('CONNECTED', 'DISCONNECTED');

-- AlterTable
ALTER TABLE "Company" ADD COLUMN     "APReportLastSyncDate" DATE,
ADD COLUMN     "ARReportLastSyncDate" DATE,
ADD COLUMN     "BalanceSheetReportLastSyncDate" DATE,
ADD COLUMN     "ProfitLossReportLastSyncDate" DATE,
ADD COLUMN     "TransactionReportLastSyncDate" DATE,
ADD COLUMN     "qboAccessToken" TEXT,
ADD COLUMN     "qboAccessTokenCreatedAt" TIMESTAMP(3),
ADD COLUMN     "qboCompanyName" TEXT,
ADD COLUMN     "qboConnectionStatus" "QboConnectionStatus" NOT NULL DEFAULT 'DISCONNECTED',
ADD COLUMN     "qboRealmID" TEXT,
ADD COLUMN     "qboRefreshToken" TEXT NOT NULL DEFAULT 'null',
ADD COLUMN     "qboTokenExpiryAt" TIMESTAMP(6),
ADD COLUMN     "tokenExpiryAtUtcDateTime" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "Account" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "connectionId" INTEGER,
    "name" VARCHAR(255) NOT NULL,
    "fullyQualifiedAccountName" VARCHAR(500) NOT NULL,
    "type" VARCHAR(100) NOT NULL,
    "isActiveAccount" BOOLEAN,
    "currencyCode" VARCHAR(10),
    "accountSubTypeName" VARCHAR(100),
    "accountClassification" VARCHAR(100),
    "currentAccountBalance" DECIMAL(15,2),
    "isSubAccountFlag" BOOLEAN,
    "parentAccountQuickbooksId" VARCHAR(100),
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AccountPayableAgingSummaryReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "realmId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "total" DECIMAL(15,2) NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AccountPayableAgingSummaryReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AccountReceivableAgingSummaryReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "realmId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "total" DECIMAL(15,2) NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AccountReceivableAgingSummaryReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BalanceSheetReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "statementAmount" DECIMAL(15,2) NOT NULL,
    "currencyCode" VARCHAR(10) NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "BalanceSheetReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProfitLossReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "amount" DECIMAL(15,2) NOT NULL,
    "currencyCode" VARCHAR(10) NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProfitLossReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrialBalanceReport" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "realmId" VARCHAR(100) NOT NULL,
    "accountId" VARCHAR(100) NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "monthEndDebitAmount" DECIMAL(15,2) NOT NULL,
    "monthEndCreditAmount" DECIMAL(15,2) NOT NULL,
    "netChangeAmount" DECIMAL(15,2) NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "modifiedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TrialBalanceReport_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Account_userId_idx" ON "Account"("userId");

-- CreateIndex
CREATE INDEX "Account_realmId_idx" ON "Account"("realmId");

-- CreateIndex
CREATE INDEX "Account_accountId_idx" ON "Account"("accountId");

-- CreateIndex
CREATE INDEX "Account_connectionId_idx" ON "Account"("connectionId");

-- CreateIndex
CREATE INDEX "Account_type_idx" ON "Account"("type");

-- CreateIndex
CREATE INDEX "AccountPayableAgingSummaryReport_userId_idx" ON "AccountPayableAgingSummaryReport"("userId");

-- CreateIndex
CREATE INDEX "AccountPayableAgingSummaryReport_realmId_idx" ON "AccountPayableAgingSummaryReport"("realmId");

-- CreateIndex
CREATE INDEX "AccountPayableAgingSummaryReport_year_month_idx" ON "AccountPayableAgingSummaryReport"("year", "month");

-- CreateIndex
CREATE INDEX "AccountReceivableAgingSummaryReport_userId_idx" ON "AccountReceivableAgingSummaryReport"("userId");

-- CreateIndex
CREATE INDEX "AccountReceivableAgingSummaryReport_realmId_idx" ON "AccountReceivableAgingSummaryReport"("realmId");

-- CreateIndex
CREATE INDEX "AccountReceivableAgingSummaryReport_year_month_idx" ON "AccountReceivableAgingSummaryReport"("year", "month");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_userId_idx" ON "BalanceSheetReport"("userId");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_realmId_idx" ON "BalanceSheetReport"("realmId");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_accountId_idx" ON "BalanceSheetReport"("accountId");

-- CreateIndex
CREATE INDEX "BalanceSheetReport_year_month_idx" ON "BalanceSheetReport"("year", "month");

-- CreateIndex
CREATE INDEX "ProfitLossReport_userId_idx" ON "ProfitLossReport"("userId");

-- CreateIndex
CREATE INDEX "ProfitLossReport_realmId_idx" ON "ProfitLossReport"("realmId");

-- CreateIndex
CREATE INDEX "ProfitLossReport_accountId_idx" ON "ProfitLossReport"("accountId");

-- CreateIndex
CREATE INDEX "ProfitLossReport_year_month_idx" ON "ProfitLossReport"("year", "month");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_userId_idx" ON "TrialBalanceReport"("userId");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_realmId_idx" ON "TrialBalanceReport"("realmId");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_accountId_idx" ON "TrialBalanceReport"("accountId");

-- CreateIndex
CREATE INDEX "TrialBalanceReport_year_month_idx" ON "TrialBalanceReport"("year", "month");

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountPayableAgingSummaryReport" ADD CONSTRAINT "AccountPayableAgingSummaryReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AccountReceivableAgingSummaryReport" ADD CONSTRAINT "AccountReceivableAgingSummaryReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BalanceSheetReport" ADD CONSTRAINT "BalanceSheetReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfitLossReport" ADD CONSTRAINT "ProfitLossReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrialBalanceReport" ADD CONSTRAINT "TrialBalanceReport_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
