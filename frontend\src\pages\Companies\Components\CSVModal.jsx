import React, { useState, useRef } from 'react';
import { Upload, X } from 'lucide-react';
import { uploadDoc } from '../../../services/company';
import Swal from "sweetalert2";

const CSVUploadModal = ({ onClose, onUpload, companyId, type, onSuccess }) => {
    const [isDragging, setIsDragging] = useState(false);
    const [selectedFile, setSelectedFile] = useState(null);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [isUploading, setIsUploading] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const fileInputRef = useRef(null);

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = () => {
        setIsDragging(false);
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        const file = e.dataTransfer.files[0];
        setSelectedFile(file);
    };

    const handleFileSelect = (e) => {
        const file = e.target.files[0];
        setSelectedFile(file);
        setUploadProgress(0);
    };

    const simulateUpload = async (file) => {
        setIsUploading(true);
        for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise(resolve => setTimeout(resolve, 200));
            setUploadProgress(progress);
        }
        setIsUploading(false);
        onUpload(file);
    }

    const handleUpload = async () => {
        if (selectedFile) {
            setIsProcessing(true); // Set processing state to true at start
            try {
                await simulateUpload(selectedFile);
                
                const fileName = selectedFile.name;
                const base64String = await readFileAsBase64(selectedFile);
                
                const payload = {
                    file: base64String,
                    name: fileName,
                    type,
                };
                
                const response = await uploadDoc(companyId, payload);
                
                if (response.data.status === 200) {
                    // Keep processing state true during success handling
                    if (onSuccess) {
                        await onSuccess();
                    }
                    setIsProcessing(false); 
                    onClose();
                    Swal.fire({
                        toast: true,
                        position: "top-end",
                        icon: "success",
                        title: "File uploaded successfully!",
                        showConfirmButton: false,
                        timer: 1000,
                        timerProgressBar: true,
                    });

                }
            } catch (error) {
                console.error("Error converting file to Base64 or uploading:", error);
                Swal.fire({
                    toast: true,
                    position: "top-end",
                    icon: "error",
                    title: "Failed to Upload file. Please try again.",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                });
                setIsProcessing(false); // Reset processing state on error
            }
        }
    };

    const readFileAsBase64 = (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64String = reader.result.split(',')[1];
                resolve(base64String);
            };
            reader.onerror = (error) => {
                reject(error);
            };
            reader.readAsDataURL(file);
        });
    };

    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(0)) + ' ' + sizes[i];
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">Upload</h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700"
                        disabled={isUploading || isProcessing}
                    >
                        <X size={20} />
                    </button>
                </div>

                <p className="text-sm text-gray-600 mb-2">Upload the latest chart of accounts.</p>

                {!selectedFile && (
                    <div
                        className={`border-2 border-dashed rounded-lg p-8 mb-4 text-center ${
                            isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
                        }`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                        onClick={() => fileInputRef.current?.click()}
                    >
                        <Upload className="mx-auto mb-4 text-blue-600" size={24} />
                        <div className="text-blue-600 font-medium mb-1">
                            Click to upload or drag and drop
                        </div>
                        <div className="text-sm text-gray-500">
                           XLSX, CSV file supported (max. 2MB)
                        </div>
                        <input
                            ref={fileInputRef}
                            type="file"
                            accept=".xlsx,.csv"
                            onChange={handleFileSelect}
                            className="hidden"
                        />
                    </div>
                )}

                {selectedFile && (
                    <div className="mb-4">
                        <div className="flex items-start gap-3 mb-2">
                            <div className="w-6 h-6 flex-shrink-0">
                                <svg className="w-6 h-6 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M3 6.5a1 1 0 011-1h8a1 1 0 011 1v11a1 1 0 01-1 1H4a1 1 0 01-1-1v-11z" />
                                    <path d="M14 6.5a1 1 0 011-1h4a1 1 0 011 1v11a1 1 0 01-1 1h-4a1 1 0 01-1-1v-11z" />
                                </svg>
                            </div>
                            <div className="flex-1">
                                <div className="text-sm font-medium">{selectedFile.name}</div>
                                <div className="text-sm text-gray-500">
                                    {formatFileSize(parseInt(selectedFile.size))}
                                </div>
                                <div className="mt-2 bg-gray-200 rounded-full h-2 overflow-hidden">
                                    <div
                                        className="h-full bg-blue-600 rounded-full transition-all duration-300"
                                        style={{ width: `${uploadProgress}%` }}
                                    />
                                </div>
                            </div>
                            {!isUploading && !isProcessing && (
                                <button
                                    onClick={() => setSelectedFile(null)}
                                    className="text-gray-500 hover:text-gray-700"
                                >
                                    <X size={16} />
                                </button>
                            )}
                        </div>
                    </div>
                )}

                <div className="flex gap-3">
                    <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={isUploading || isProcessing}
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handleUpload}
                        className={`flex-1 px-4 py-2 rounded-lg text-white ${
                            selectedFile && !isUploading && !isProcessing
                                ? 'bg-blue-600 hover:bg-blue-700'
                                : 'bg-blue-300 cursor-not-allowed'
                        }`}
                        disabled={!selectedFile || isUploading || isProcessing}
                    >
                        {isUploading ? 'Uploading...' : isProcessing ? 'Uploading...' : 'Upload'}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CSVUploadModal;