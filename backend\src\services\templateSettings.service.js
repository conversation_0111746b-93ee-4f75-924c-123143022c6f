import { prisma } from '../db/prisma.js';
import { createErrorResponse, createSuccessResponse } from '../utils/response.js';
import { HttpStatusCode } from '../enums/error.enum.js';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/errorHandler.js';

// Add supported report types here, e.g., ['SOME_REPORT_TYPE']
const validReportTypes = ['DEEPSIGHT'];

/**
 * Validate report type
 */
const validateReportType = (reportType) => {
  if (!validReportTypes.includes(reportType?.toUpperCase())) {
    throw new ErrorHandler(
      `Unsupported type of report. Supported types: ${validReportTypes.join(', ')}`,
      400
    );
  }
};

// Default template settings to be used for seeding
export const DEFAULT_TEMPLATE_SETTINGS = {
  header: {
    fontStyle: "Helvetica",
    fontType: "Bold",
    fontSize: 44,
    color: "#1e7c8c"
  },
  heading: {
    fontStyle: "Helvetica",
    fontType: "Bold",
    fontSize: 36,
    color: "#1e7c8c"
  },
  subHeading: {
    fontStyle: "Helvetica",
    fontType: "Bold",
    fontSize: 22,
    color: "#1e7c8c"
  },
  content: {
    fontStyle: "Helvetica",
    fontType: "Regular",
    fontSize: 15,
    color: "#333333"
  }
};

/**
 * Get template settings for a user
 * Returns custom settings if they exist, otherwise returns global default
 */
export const getTemplateSettings = async (userId, reportType = 'DEEPSIGHT') => {
  try {
    // Validate report type
    validateReportType(reportType);

    const normalizedReportType = reportType.toUpperCase();

    // First, check if user has custom settings
    const userCustomSettings = await prisma.templateSettings.findFirst({
      where: {
        userId: userId,
        reportType: normalizedReportType,
        templateType: 'CUSTOM'
      },
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    if (userCustomSettings) {
      return createSuccessResponse(
        HttpStatusCode.OK,
        'Custom template settings retrieved successfully',
        {
          settings: userCustomSettings.settings,
          isCustom: true,
          templateType: 'CUSTOM',
          lastUpdatedBy: userCustomSettings.updatedByUser,
          updatedAt: userCustomSettings.updatedAt
        }
      );
    }

    // If no custom settings, get global default
    const globalSettings = await prisma.templateSettings.findFirst({
      where: {
        userId: null,
        reportType: normalizedReportType,
        templateType: 'GLOBAL'
      },
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    if (!globalSettings) {
      // If no global settings exist, create them with default values
      await createGlobalDefaultSettings(null, normalizedReportType);

      return createSuccessResponse(
        HttpStatusCode.OK,
        'Default template settings retrieved successfully',
        {
          settings: DEFAULT_TEMPLATE_SETTINGS,
          isCustom: false,
          templateType: 'GLOBAL',
          lastUpdatedBy: null,
          updatedAt: new Date()
        }
      );
    }

    return createSuccessResponse(
      HttpStatusCode.OK,
      'Global template settings retrieved successfully',
      {
        settings: globalSettings.settings,
        isCustom: false,
        templateType: 'GLOBAL',
        lastUpdatedBy: globalSettings.updatedByUser,
        updatedAt: globalSettings.updatedAt
      }
    );

  } catch (error) {
    console.error('Error fetching template settings:', error);
    return createErrorResponse(
      HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to fetch template settings'
    );
  }
};


//get global settings
export const getGlobalTemplateSettings = async (userId, reportType = 'DEEPSIGHT') => {
  try {
    // Validate report type
    validateReportType(reportType);

    const normalizedReportType = reportType.toUpperCase();

    // First, check if user has custom settings
    const userCustomSettings = await prisma.templateSettings.findFirst({
      where: {
        // userId: userId,
        reportType: normalizedReportType,
        templateType: 'GLOBAL'
      },
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    if (userCustomSettings) {
      return createSuccessResponse(
        HttpStatusCode.OK,
        'Global template settings retrieved successfully',
        {
          settings: userCustomSettings.settings,
          isCustom: true,
          templateType: 'CUSTOM',
          lastUpdatedBy: userCustomSettings.updatedByUser,
          updatedAt: userCustomSettings.updatedAt
        }
      );
    }

    // If no custom settings, get global default
    const globalSettings = await prisma.templateSettings.findFirst({
      where: {
        userId: null,
        reportType: normalizedReportType,
        templateType: 'GLOBAL'
      },
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    if (!globalSettings) {
      // If no global settings exist, create them with default values
      await createGlobalDefaultSettings(null, normalizedReportType);

      return createSuccessResponse(
        HttpStatusCode.OK,
        'Default template settings retrieved successfully',
        {
          settings: DEFAULT_TEMPLATE_SETTINGS,
          isCustom: false,
          templateType: 'GLOBAL',
          lastUpdatedBy: null,
          updatedAt: new Date()
        }
      );
    }

    return createSuccessResponse(
      HttpStatusCode.OK,
      'Global template settings retrieved successfully',
      {
        settings: globalSettings.settings,
        isCustom: false,
        templateType: 'GLOBAL',
        lastUpdatedBy: globalSettings.updatedByUser,
        updatedAt: globalSettings.updatedAt
      }
    );

  } catch (error) {
    console.error('Error fetching template settings:', error);
    return createErrorResponse(
      HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to fetch template settings'
    );
  }
};

/**
 * Update template settings
 * For admins: Updates global settings (userId set to null)
 * For regular users: Creates/updates custom settings
 */
export const updateTemplateSettings = async (userId, settings, isAdmin, reportType = 'DEEPSIGHT') => {
  try {
    // Validate report type
    validateReportType(reportType);

    const normalizedReportType = reportType.toUpperCase();

    // Validate settings structure
    const validationError = validateTemplateSettings(settings);
    if (validationError) {
      return createErrorResponse(
        HttpStatusCode.BAD_REQUEST,
        validationError
      );
    }

    if (isAdmin) {
      // Admin updates global settings - don't pass any userId in update (keep it null)
      return await updateGlobalSettings(settings, null, normalizedReportType);
    } else {
      // Regular user creates/updates custom settings
      return await updateUserCustomSettings(userId, settings, normalizedReportType);
    }

  } catch (error) {
    console.error('Error updating template settings:', error);

    // Handle ErrorHandler instances
    if (error instanceof ErrorHandler) {
      return createErrorResponse(
        error.statusCode,
        error.message
      );
    }

    return createErrorResponse(
      HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to update template settings'
    );
  }
};

/**
 * Update global template settings (Admin only)
 */
const updateGlobalSettings = async (settings, adminUserId, reportType) => {
  try {
    const updatedSettings = await prisma.templateSettings.upsert({
      where: {
        unique_global_per_report_type: {
          templateType: 'GLOBAL',
          reportType: reportType
        }
      },
      update: {
        settings: settings,
        updatedBy: adminUserId,
        updatedAt: new Date()
      },
      create: {
        userId: null,
        reportType: reportType,
        templateType: 'GLOBAL',
        settings: settings,
        updatedBy: adminUserId
      },
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    return createSuccessResponse(
      HttpStatusCode.OK,
      'Global template settings updated successfully',
      {
        settings: updatedSettings.settings,
        templateType: 'GLOBAL',
        lastUpdatedBy: updatedSettings.updatedByUser,
        updatedAt: updatedSettings.updatedAt
      }
    );

  } catch (error) {
    console.error('Error updating global settings:', error);
    throw error;
  }
};

/**
 * Update user custom template settings
 */
const updateUserCustomSettings = async (userId, settings, reportType) => {
  try {
    const updatedSettings = await prisma.templateSettings.upsert({
      where: {
        userId_reportType_templateType: {
          userId: userId,
          reportType: reportType,
          templateType: 'CUSTOM'
        }
      },
      update: {
        settings: settings,
        updatedBy: userId,
        updatedAt: new Date()
      },
      create: {
        userId: userId,
        reportType: reportType,
        templateType: 'CUSTOM',
        settings: settings,
        updatedBy: userId
      },
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    return createSuccessResponse(
      HttpStatusCode.OK,
      'Custom template settings updated successfully',
      {
        settings: updatedSettings.settings,
        templateType: 'CUSTOM',
        lastUpdatedBy: updatedSettings.updatedByUser,
        updatedAt: updatedSettings.updatedAt
      }
    );

  } catch (error) {
    console.error('Error updating user custom settings:', error);
    throw error;
  }
};

/**
 * Create global default settings
 */
export const createGlobalDefaultSettings = async (adminUserId = null, reportType = 'DEEPSIGHT') => {
  try {
    // Validate report type
    validateReportType(reportType);

    const normalizedReportType = reportType.toUpperCase();

    const existingGlobalSettings = await prisma.templateSettings.findFirst({
      where: {
        userId: null,
        reportType: normalizedReportType,
        templateType: 'GLOBAL'
      }
    });

    if (existingGlobalSettings) {
      console.log('Global default settings already exist');
      return;
    }

    await prisma.templateSettings.create({
      data: {
        userId: null,
        reportType: normalizedReportType,
        templateType: 'GLOBAL',
        settings: DEFAULT_TEMPLATE_SETTINGS,
        updatedBy: adminUserId
      }
    });

    console.log('Global default template settings created successfully');
  } catch (error) {
    console.error('Error creating global default settings:', error);
    throw error;
  }
};

/**
 * Validate template settings structure
 */
const validateTemplateSettings = (settings) => {
  const requiredSections = ['header', 'heading', 'subHeading', 'content'];
  const requiredProperties = ['fontStyle', 'fontType', 'fontSize', 'color'];

  for (const section of requiredSections) {
    if (!settings[section]) {
      return `Missing required section: ${section}`;
    }

    for (const property of requiredProperties) {
      if (!settings[section][property]) {
        return `Missing required property '${property}' in section '${section}'`;
      }
    }

    // Validate fontSize is a number
    if (typeof settings[section].fontSize !== 'number') {
      return `fontSize must be a number in section '${section}'`;
    }

    // Validate color format (basic hex color validation)
    if (!/^#[0-9A-Fa-f]{6}$/.test(settings[section].color)) {
      return `Invalid color format in section '${section}'. Use hex format like #1e7c8c`;
    }
  }

  return null; // No validation errors
};

