import { prisma } from "../db/prisma.js"

export const getAllUsers = async (userId, page, pageSize, search) => {
    try {

        // 1) First fetch all the user companies
        let userCompanies = await prisma.company.findMany({
            where: {
                userId
            },
            select: {
                id: true
            }
        })

        let userCompaniesIds = userCompanies.map(item => item.id);

        // now find all the users which have this companies assign 
        let userAssignedCompanies = await prisma.sharedCompanies.findMany({
            where: {
                companyId: {
                    in: userCompaniesIds
                }
            },
            select: {
                userId: true
            }
        })
        let assignCompaniesUserIds = [
            ...new Set([
                ...(userAssignedCompanies?.map(item => item.userId) || []),
                userId
            ])
        ]

        // where class payload
        let whereCondition = {
            id: {
                not: {
                    in: assignCompaniesUserIds
                }
            },
            username: {
                contains: search,
                mode: 'insensitive',
            },
            isAdmin: false
        }

        // find all the users
        let users = await prisma.user.findMany({
            where: whereCondition,
            skip: (page - 1) * pageSize,
            take: pageSize
        })

        // get all users withour pagination 

        let totalUsers = await prisma.user.count({ where: whereCondition })


        return {
            success: true,
            statusCode: 200,
            users,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(totalUsers / pageSize),
                totalUsers,
                pageSize,
            },
        }
    } catch (error) {
        throw {
            success: false,
            statusCode: error.statusCode || 500,
            message: 'Error while fetching users',
        };
    }
}



export const addSharedUser = async (userId, currentUserId) => {
    try {
        // fetch all the current company of login user
        let companies = await prisma.company.findMany({
            where: {
                userId: currentUserId
            },
            select: {
                id: true
            }
        })

        // payload 
        let payload = companies.map((company) => ({
            userId,
            companyId: company.id
        }));
        
        let sharedCompanies = await prisma.sharedCompanies.createMany({
            data: payload
        })

        console.log("shared companies -----", sharedCompanies)

        return {
            success: true,
            message: "Companies shared with users successfully.",
            sharedCompanies: sharedCompanies.count
        };

    } catch (error) {
        throw {
            success: false,
            statusCode: error.statusCode || 500,
            message: 'Error while adding user.',
        };
    }
}


export const getSharedUser = async (userId, page, pageSize) => {
    try {
        // get current user companies
        let userCompanies = await prisma.company.findMany({
            where: {
                userId
            },
            select: {
                id: true
            }
        })

        const userCompaniesId = userCompanies.map((item) => item.id);

        // get all the users which are belong to thier companyes
        const companiesSharing = await prisma.sharedCompanies.findMany({
            where: {
                AND: [
                    {
                        userId: {
                            not: userId
                        }
                    },
                    {
                        companyId: {
                            in: userCompaniesId
                        }
                    },
                ]
            },
            distinct: ["userId"],
            include: {
                users: {
                    select: {
                        id: true,
                        username: true,
                        isAdmin: true,
                        createdAt: true,
                        email: true
                    }
                }
            },
            skip: (page - 1) * pageSize,
            take: pageSize
        });

        const totalDistinctUsers = await prisma.sharedCompanies.groupBy({
            by: ['userId'],
            where: {
                userId: { not: userId },
                companyId: { in: userCompaniesId }
            }
        });

        return {
            success: true,
            statusCode: 200,
            sharedCompanyUsers: companiesSharing,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(totalDistinctUsers.length / pageSize),
                totalUsers: totalDistinctUsers.length,
                pageSize,
            },

        }

    } catch (error) {
        console.log({ error });

        throw {
            success: false,
            statusCode: error.statusCode || 500,
            message: 'Error while getting users.',
        };
    }
}

export const deleteSharedUser = async (userId, targetUserId) => {
    try {
        // get all the curent user companies
        let userCompanies = await prisma.company.findMany({
            where: {
                userId
            },
            select: {
                id: true
            }
        })


        const userCompaniesId = userCompanies.map((item) => item.id);

        // now delete the shared user that have access to current user companies
        let userDeleted = await prisma.sharedCompanies.deleteMany({
            where: {
                AND: [
                    { userId: targetUserId },
                    {
                        companyId: {
                            in: userCompaniesId
                        }
                    }
                ]
            }
        })

        return {
            success: true,
            statusCode: 200,
            message: "User is deleted successfully."
        }

    } catch (error) {
        throw {
            success: false,
            statusCode: error.statusCode || 500,
            message: 'Error while deleting user.',
        };
    }
}