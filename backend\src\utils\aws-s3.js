
import AWS from 'aws-sdk';
import { fileTypeFromBuffer } from 'file-type';

const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,  // Make sure to use environment variables for credentials
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,  // specify your region
});

export const generateSignedUrl = async (fileName) => {
    const params = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: fileName,
        Expires: 7 * 24 * 60 * 60, // The URL will expire in 7 days
    };

    try {
        return s3.getSignedUrl('getObject', params);
    } catch (error) {
        console.error('Error generating signed URL:', error);
        throw new Error('Unable to generate signed URL');
    }
}

export const uploadFileOnS3 = async (base64File, fileName) => {
    const buffer = Buffer.from(base64File, 'base64'); // Remove metadata prefix if needed
    let fileType = await fileTypeFromBuffer(buffer);

    if (!fileType) {
        const fileExtension = getFileExtension(buffer);
        fileType = { ext: fileExtension, mime: getMimeType(fileExtension) };
    }

    const mimeType = fileType?.mime || 'application/octet-stream';
    const fileNameWithExtension = `${fileName}.${fileType.ext}`;

    const params = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: `${fileNameWithExtension}`,
        Body: buffer,
        ContentType: mimeType
    };

    try {
        return await s3.upload(params).promise();
    } catch (error) {
        console.error('Error uploading image:', error);
        throw new Error('Error uploading image on S3');
    }
}

export const deleteFileFromS3 = async (key) => {
    const params = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: key,
    };

    try {
        await s3.deleteObject(params).promise();
        console.log(`File deleted successfully from S3: ${key}`);
    } catch (error) {
        console.error('Error deleting file from S3:', error);
        throw new Error('Failed to delete file from S3');
    }
}

const getFileExtension = (buffer) => {
    const asciiContent = buffer.toString('ascii', 0, 10);
    if (asciiContent.startsWith('%PDF')) return 'pdf';
    if (asciiContent.startsWith('GIF8')) return 'gif';
    if (asciiContent.startsWith('PNG')) return 'png';
    if (buffer.toString('ascii', 0, 2) === '\xFF\xD8') return 'jpg';
    if (asciiContent.startsWith('PK\x03\x04')) return 'xlsx';
    if (asciiContent.startsWith('ID3')) return 'mp3';
    if (asciiContent.includes('<svg')) return 'svg'; // Check for <svg tag
    return 'csv';
}

const getMimeType = (extension) => {
    switch (extension) {
        case 'pdf': return 'application/pdf';
        case 'gif': return 'image/gif';
        case 'jpg':
        case 'jpeg': return 'image/jpeg';
        case 'png': return 'image/png';
        case 'svg': return 'image/svg+xml';
        case 'xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        case 'csv': return 'text/csv';
        default: return 'application/octet-stream'; // Fallback for unknown types
    }
}

export const getFileAsBase64 = async (key) => {
    try {
        // Get the file from S3
        const params = { Bucket: process.env.S3_BUCKET_NAME, Key: key };
        const data = await s3.getObject(params).promise();

        // Convert the file data to Base64
        return data.Body.toString('base64');
    } catch (error) {
        console.error('Error retrieving file from S3:', error);
        throw error;
    }
}