// backend\src\services\contentSettings.service.js
import { prisma } from '../db/prisma.js';

import { ErrorHandler } from '../utils/errorHandler.js';

export const getAll = async (req) => {
  const { companyId } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new ErrorHandler('User not authenticated', 401);
  }

  if (!companyId) {
    throw new ErrorHandler('Company ID is required', 400);
  }

  // Verify user has access to this company (admin users can access any company)
  if (!req.user?.isAdmin) {
    const company = await prisma.company.findFirst({
      where: {
        id: parseInt(companyId),
        OR: [
          { userId: userId }, // User owns the company
          { sharedCompanies: { some: { userId: userId } } } // User has shared access
        ]
      }
    });

    if (!company) {
      throw new ErrorHandler('Access denied to this company', 403);
    }
  } else {
    // For admin users, just verify the company exists
    const company = await prisma.company.findUnique({
      where: { id: parseInt(companyId) }
    });

    if (!company) {
      throw new ErrorHandler('Company not found', 404);
    }
  }

  const data = await prisma.reportContentSettings.findMany({
    where: { companyId: parseInt(companyId) },
  });
  return {
    success: true,
    statusCode: 200,
    data,
  };
};

export const getByReportType = async (req) => {
  const { companyId, reportType } = req.params;
  const userId = req.user?.id;

  if (!userId) {
    throw new ErrorHandler('User not authenticated', 401);
  }

  if (!companyId) {
    throw new ErrorHandler('Company ID is required', 400);
  }

  if (!reportType) {
    throw new ErrorHandler('Missing reportType param', 400);
  }

  // Add supported report types here, e.g., ['SOME_REPORT_TYPE']
  const validReportTypes = ['DEEPSIGHT'];
  if (!validReportTypes.includes(reportType?.toUpperCase())) {
    throw new ErrorHandler(
      `Unsupported type of report. Supported types: ${validReportTypes.join(', ')}`,
      400,
    );
  }

  // Verify user has access to this company (admin users can access any company)
  if (!req.user?.isAdmin) {
    const company = await prisma.company.findFirst({
      where: {
        id: parseInt(companyId),
        OR: [
          { userId: userId }, // User owns the company
          { sharedCompanies: { some: { userId: userId } } } // User has shared access
        ]
      }
    });

    if (!company) {
      throw new ErrorHandler('Access denied to this company', 403);
    }
  } else {
    // For admin users, just verify the company exists
    const company = await prisma.company.findUnique({
      where: { id: parseInt(companyId) }
    });

    if (!company) {
      throw new ErrorHandler('Company not found', 404);
    }
  }

  const data = await prisma.reportContentSettings.findFirst({
    where: {
      reportType: reportType.toUpperCase(),
      companyId: parseInt(companyId),
    },
  });

  if (!data) {
    return {
      success: true,
      statusCode: 200,
      data: [],
    };
  }

  return {
    success: true,
    statusCode: 200,
    data,
  };
};

export const updateByReportType = async (req) => {
  const { companyId, reportType } = req.params;
  const { chartSettings, promptDescription } = req.body;
  const userId = req.user?.id;

  if (!userId) {
    throw new ErrorHandler('User not authenticated', 401);
  }

  if (!companyId) {
    throw new ErrorHandler('Company ID is required', 400);
  }

  if (!reportType) {
    throw new ErrorHandler('Missing reportType param', 400);
  }

  // Validate required fields
  if (!chartSettings && !promptDescription) {
    throw new ErrorHandler(
      'At least one field (chartSettings or promptDescription) must be provided',
      400,
    );
  }

  // Validate chartSettings if provided
  if (chartSettings && typeof chartSettings !== 'object') {
    throw new ErrorHandler('chartSettings must be a valid JSON object', 400);
  }

  // Verify user has access to this company (admin users can access any company)
  if (!req.user?.isAdmin) {
    const company = await prisma.company.findFirst({
      where: {
        id: parseInt(companyId),
        OR: [
          { userId: userId }, // User owns the company
          { sharedCompanies: { some: { userId: userId } } } // User has shared access
        ]
      }
    });

    if (!company) {
      throw new ErrorHandler('Access denied to this company', 403);
    }
  } else {
    // For admin users, just verify the company exists
    const company = await prisma.company.findUnique({
      where: { id: parseInt(companyId) }
    });

    if (!company) {
      throw new ErrorHandler('Company not found', 404);
    }
  }

  // Check if the record exists for this company
  const existingRecord = await prisma.reportContentSettings.findFirst({
    where: {
      reportType: reportType.toUpperCase(),
      companyId: parseInt(companyId),
    },
  });

  // If no existing record, create a new one
  if (!existingRecord) {
    const newRecord = await prisma.reportContentSettings.create({
      data: {
        companyId: parseInt(companyId),
        reportType: reportType.toUpperCase(),
        chartSettings: chartSettings || {},
        promptDescription: promptDescription || '',
        updatedBy: userId,
      },
      include: req.user?.isAdmin ? {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      } : {},
    });

    return {
      success: true,
      statusCode: 201,
      message: 'Content settings created successfully',
      data: newRecord,
    };
  }

  // Prepare update data
  const updateData = {
    updatedAt: new Date(),
    updatedBy: userId,
  };

  if (chartSettings !== undefined) {
    updateData.chartSettings = chartSettings;
  }

  if (promptDescription !== undefined) {
    updateData.promptDescription = promptDescription;
  }

  // Conditionally include updatedByUser based on admin status
  const includeOptions = {};
  if (req.user?.isAdmin) {
    includeOptions.updatedByUser = {
      select: {
        id: true,
        username: true,
        email: true,
      },
    };
  }

  const updatedRecord = await prisma.reportContentSettings.update({
    where: { id: existingRecord.id },
    data: updateData,
    include: includeOptions,
  });

  return {
    success: true,
    statusCode: 200,
    message: 'Content settings updated successfully',
    data: updatedRecord,
  };
};
