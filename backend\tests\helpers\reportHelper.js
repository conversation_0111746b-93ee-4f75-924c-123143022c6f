import { prisma } from "../../src/db/prisma.js";
export const customReport  = async (companyId)=>{
        // Create a new test report
        const report = await prisma.reportRequest.create({
            data: {
                name: "Test Report",
                request_type: "monthly",
                date_requested: new Date().toISOString(), // Current DateTime
                companyId: companyId, // Ensure valid ID
                status: "edit",
                cashflow: false, // Boolean value
            },
        });
        return report
}

