import React, {useState} from "react";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import logo from "../../../assets/checkEmail.svg";
import { forgotPassword } from "../../../services/auth";
import Swal from "sweetalert2";

const CheckEmail = () => {
  const navigate = useNavigate();
  const email = Cookies.get("email");
  const [isResending, setIsResending] = useState(false);

  const handleResendEmail = async()=>{

    if (!email) {
        Swal.fire({
          icon: "error",
          title: "Error!",
          text: "Email not found. Please try again later.",
        });
        return;
      }
  
      setIsResending(true);
      try {
        await forgotPassword(email);
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Reset email has been sent. Please check your inbox.",
        });
      } catch (error) {
        Swal.fire({
          icon: "error",
          title: "Error!",
          text: error.message || "Failed to resend email.",
        });
      } finally {
        setIsResending(false);
      }
    };

  return (
    <div className="flex items-center justify-center h-screen bg-gray-50">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg text-center">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <img src={logo} alt="Logo" className="w-12 h-12" />
        </div>

        {/* Title */}
        <h2 className="text-2xl font-semibold mb-2">Check your email</h2>
        <p className="text-sm text-gray-500 mb-4">
          We sent a password reset link to <br /> <strong>{email}</strong>
        </p>

        {/* Resend Link */}
        <p className="text-sm text-gray-500 mb-4">
          Didn't receive the email?{" "}
          <button
            className={`text-blue-500 hover:underline ${isResending && "cursor-not-allowed"}`}
            onClick={handleResendEmail}
            disabled={isResending}
          >
            {isResending ? "Resending..." : "Click to resend"}
          </button>
        </p>

        {/* Back to Log In */}
        <button
          onClick={() => navigate("/login")}
          className="text-sm text-blue-500 hover:underline"
        >
          ← Back to log in
        </button>
      </div>
    </div>
  );
};

export default CheckEmail;
