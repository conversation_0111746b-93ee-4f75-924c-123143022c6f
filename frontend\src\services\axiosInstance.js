import axios from "axios";
import Cookies from "js-cookie";

// endpoints where the token should NOT be attached
const noAuthEndpoints = [
  "/auth/login",
  "/auth/forgot-password",
  "/auth/reset-password",
  "/auth/reset-password-email",
  "/qbo-callback", // Add QBO callback to no-auth endpoints
];

const axiosInstance = axios.create({
  baseURL: `${process.env.REACT_APP_API_URL}/api/v1`,
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token = Cookies.get("auth_token");

    // Check if the current request URL is in the "noAuthEndpoints" list
    const isNoAuthEndpoint = noAuthEndpoints?.some((endpoint) =>
      config.url.includes(endpoint)
    );

    if (token && !isNoAuthEndpoint) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response) {
      if (error.response.status === 401) {
        // Only redirect to login if it's not a QBO-related endpoint
        if (!error.config.url.includes("/qbo/")) {
          Cookies.remove("auth_token");
          window.location.href = "/login";
        }
      }
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
