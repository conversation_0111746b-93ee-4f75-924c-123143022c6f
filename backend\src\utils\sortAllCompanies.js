export const sortAllCompanies = (allCompaniesData, sortBy, sortOrder) => {
    return allCompaniesData.sort((a, b) => {

        if (sortBy === "companyName") {
            return sortOrder === "asc"
                ? a.name.localeCompare(b.name)
                : b.name.localeCompare(a.name);
        }


        if (sortBy === "lastRequest") {
            const dateA = a.lastRequest ? new Date(a.lastRequest) : new Date(0);
            const dateB = b.lastRequest ? new Date(b.lastRequest) : new Date(0);
            return sortOrder === "asc" ? dateA - dateB : dateB - dateA; // Latest first in desc order
        }

        if (sortBy === "owner") {
            const isMeA = a.owner.includes("(Me)");
            const isMeB = b.owner.includes("(Me)");

            if (isMeA !== isMeB) return isMeA ? -1 : 1;
            return sortOrder === "asc"
                ? a.owner.localeCompare(b.owner)
                : b.owner.localeCompare(a.owner);
        }

        if (sortBy === "status") {
            const statusOrder = { edit: 1, pending: 2, download: 3 };
            return sortOrder === "asc"
                ? (statusOrder[a.status] || 4) - (statusOrder[b.status] || 4)
                : (statusOrder[b.status] || 4) - (statusOrder[a.status] || 4);
        }

       
        const order = sortOrder === "asc" ? 1 : -1;

        // 1. Prioritize "(Me)" in the owner field
        const isMeA = a.owner.includes("(Me)");
        const isMeB = b.owner.includes("(Me)");

        if (isMeA !== isMeB) return isMeA ? -1 : 1;

        // 2. Sort by owner name
        const ownerCompare = a.owner.localeCompare(b.owner);
        if (ownerCompare !== 0) return ownerCompare * order;
        return a.id - b.id;

    })

}