import jwt from 'jsonwebtoken';
import { prisma } from '../db/prisma.js';
export const generateAuthTokens = async (id) => {
  return jwt.sign({ id }, process.env.SECRET_KEY, {
    expiresIn: '1d',
  });
};
export const generateResetToken = async (id) => {
  return jwt.sign({ id }, process.env.SECRET_KEY, {
    expiresIn: '25min',
  });
};

export const authenticate = async (req, res, next) => {
  let token;
  try {
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      // Get token from headers
      token = req.headers.authorization.split(' ')[1];

      //   verify token
      const decoded = jwt.verify(token, process.env.SECRET_KEY);

      //   Get user from token
      req.user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          username: true,
          email: true,
          isAdmin: true
        },
      });

      if (req.user === null) {
        res.status(401).send({
          statusCode: 401,
          success: false,
          message: 'Unauthorized',
        });
      } else {
        next();
      }
    } else {
      res.status(401).send({
        statusCode: 401,
        success: false,
        message: 'Unauthorized, No Token',
      });
    }
  } catch (error) {
    console.log({ error });
    res.status(401).send({
      statusCode: 401,
      success: false,
      message: 'Unauthorized',
    });
  }
};
