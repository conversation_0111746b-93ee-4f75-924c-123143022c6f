// middleware/errorMiddleware.js

export const globalErrorHandler = (err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  const success = err.success === false ? false : true;

  res.status(statusCode).json({
    success,
    message: err.message || 'Internal server error',
    statusCode: err.statusCode,
    error: process.env.NODE_ENV === 'development' ? err.stack : undefined,
  });
};
