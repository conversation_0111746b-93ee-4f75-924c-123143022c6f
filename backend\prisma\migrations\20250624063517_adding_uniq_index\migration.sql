/*
  Warnings:

  - A unique constraint covering the columns `[userId,realmId,year,month]` on the table `AccountPayableAgingSummaryReport` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,realmId,year,month]` on the table `AccountReceivableAgingSummaryReport` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,realmId,year,month]` on the table `BalanceSheetReport` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,realmId,year,month]` on the table `ProfitLossReport` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[userId,realmId,year,month]` on the table `TrialBalanceReport` will be added. If there are existing duplicate values, this will fail.
  - Made the column `userId` on table `Account` required. This step will fail if there are existing NULL values in that column.
  - Made the column `userId` on table `AccountPayableAgingSummaryReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `userId` on table `AccountReceivableAgingSummaryReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `userId` on table `BalanceSheetReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `userId` on table `ProfitLossReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `userId` on table `TrialBalanceReport` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Account" ALTER COLUMN "userId" SET NOT NULL,
ALTER COLUMN "modifiedAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "AccountPayableAgingSummaryReport" ALTER COLUMN "userId" SET NOT NULL,
ALTER COLUMN "modifiedAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "AccountReceivableAgingSummaryReport" ALTER COLUMN "userId" SET NOT NULL,
ALTER COLUMN "modifiedAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "BalanceSheetReport" ALTER COLUMN "userId" SET NOT NULL,
ALTER COLUMN "modifiedAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "ProfitLossReport" ALTER COLUMN "userId" SET NOT NULL,
ALTER COLUMN "modifiedAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "TrialBalanceReport" ALTER COLUMN "userId" SET NOT NULL,
ALTER COLUMN "modifiedAt" DROP DEFAULT;

-- CreateIndex
CREATE UNIQUE INDEX "AccountPayableAgingSummaryReport_userId_realmId_year_month_key" ON "AccountPayableAgingSummaryReport"("userId", "realmId", "year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "AccountReceivableAgingSummaryReport_userId_realmId_year_mon_key" ON "AccountReceivableAgingSummaryReport"("userId", "realmId", "year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "BalanceSheetReport_userId_realmId_year_month_key" ON "BalanceSheetReport"("userId", "realmId", "year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "ProfitLossReport_userId_realmId_year_month_key" ON "ProfitLossReport"("userId", "realmId", "year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "TrialBalanceReport_userId_realmId_year_month_key" ON "TrialBalanceReport"("userId", "realmId", "year", "month");
