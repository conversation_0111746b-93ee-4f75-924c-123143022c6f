import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js";
import { getToken ,getcompanyId} from "../helpers/companyHelpers.js";

const baseUrl = `/api/v1/company`;

describe("Update Company Details API", () => {
  let authToken;
   let companyId;
 
   before(async function () {
     authToken = await getToken();
     companyId = await getcompanyId()
   });
 

  it("should update company details successfully", async () => {    
    const res = await request(app)
      .put(`${baseUrl}/${companyId}`)
      .set("Authorization", `Bearer ${authToken}`)
      .send({
        fiscal_year_end: "2025-12-31",
        country: "Canada",
        addedUsers:[]
      });
    expect(res.body.statusCode).to.equal(200);
    expect(res.body.success).to.be.true;
    expect(res.body.company.name).to.equal("Test Company");
  });

  it("should return validation error for invalid company ID", async () => {
    const res = await request(app)
      .put(`${baseUrl}/invalidID`)
      .set("Authorization", `Bearer ${authToken}`)
      .send({
        name: "Invalid Test",
      });    
    expect(res.body.statusCode).to.equal(400);
    expect(res.body.success).to.be.false;
    expect(res.body.message).to.contain("number");
  });

  it("should return unauthorized error if no token is provided", async () => {
    const res = await request(app)
      .put(`${baseUrl}/${companyId}`)
      .send({ name: "Unauthorized Test" });

    expect(res.body.statusCode).to.equal(401);
    expect(res.body.success).to.be.false;
    expect(res.body.message).to.contain("Unauthorized");
  });
});
