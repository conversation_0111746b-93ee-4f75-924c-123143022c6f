// data.js

//liquidityData.js
export const liquidityData = {
  months: [
    "Jan 24",
    "Feb 24",
    "Mar 24",
    "Apr 24",
    "May 24",
    "Jun 24",
    "Jul 24",
    "Aug 24",
    "Sep 24",
    "Oct 24",
    "Nov 24",
    "Dec 24",
    "Jan 25",
  ],

  netChangeInCash: [
    2.5, -2.2, 0.222, -0.8787, -0.088, 0.697, 0.7891, -0.3453, -0.3114, -0.9919,
    0.1696, -0.6153, 1.4,
  ],

  quickRatio: [
    1.19, 1.22, 1.06, 1.04, 1.21, 1.39, 1.58, 1.3, 1.73, 1.8, 1.95, 1.29, 1.48,
  ],

  monthsCashOnHand: [
    1.83, 1.53, 1.28, 1.4, 2.1, 1.84, 1.74, 1.26, 1.47, 1.54, 1.68, 2.19, 1.7,
  ],

  // Text content and labels
  labels: {
    mainTitle: "Liquidity Summary",
    headerSubtext: "January 2025 | Acme Print",
    netChangeTitle: "Net Change in Cash",
    quickRatioTitle: "Quick Ratio",
    monthsCashTitle: "Months Cash on Hand",
    monthsCashDescriptionTitle: "Months Cash on Hand",
    monthsCashDescription:
      "Given the amount of cash available, the number of months that a business can continue to pay for its core and operating expenses. Under 3 months or a downward trend may be cause for concern.",
  },
};

// expenseData.js
export const expenseData = {
  months: [
    "Jan 24",
    "Feb 24",
    "Mar 24",
    "Apr 24",
    "May 24",
    "Jun 24",
    "Jul 24",
    "Aug 24",
    "Sep 24",
    "Oct 24",
    "Nov 24",
    "Dec 24",
    "Jan 25",
  ],

  // ROA and ROE data
  roaData: [-1, -2, -4, 0, 2, 6, 7, 7, 6, 4, 6, 11, 7],
  roeData: [-3, -4, -8, 1, 4, 12, 13, 12, 11, 7, 11, 21, 12],

  // Expenses pie chart data
  expensesPieData: [
    3.7, 4.1, 1.0, 1.2, 0.889, 0.573, 0.431, 0.437, 0.445, 0.421, 2.9,
  ],
  expensesPieLabels: [
    "Depreciation Expense 3.7m (23.2%)",
    "Salaries - G&A 4.1m (25.4%)",
    "Sales Commissions 1m (6.4%)",
    "Rent Expense 1.2m (7.3%)",
    "Payroll Tax Expense 889.4k (5.5%)",
    "Interest Expense 573.1k (3.6%)",
    "Health Insurance - G&A 431.2k (2.7%)",
    "Salaries - Sales 436.7k (2.7%)",
    "Building Rep&Maintenance 444.7k (2.8%)",
    "Utilities Expense 421.2k (2.6%)",
    "Other 2.9m (17.8%)",
  ],

  // Monthly expenses stacked chart data
  monthlyExpensesData: {
    depreciationExpense: [
      0.36, 0.28, 0.3, 0.39, 0.34, 0.34, 0.32, 0.34, 0.26, 0.33, 0.33, 0.29,
      0.2,
    ],
    salariesGA: [
      0.43, 0.34, 0.34, 0.2, 0.3, 0.25, 0.24, 0.24, 0.27, 0.57, 0.54, 0.36,
      0.36,
    ],
    salesCommissions: [
      0.23, 0.07, 0.08, 0.15, 0.08, 0.14, 0.05, 0.06, 0.03, 0.05, 0.08, 0.15,
      0.1,
    ],
    rentExpense: [
      0.06, 0.14, 0.08, 0.08, 0.14, 0.09, 0.09, 0.1, 0.1, 0.1, 0.09, 0.07, 0.11,
    ],
    payrollTaxExpense: [
      0.11, 0.08, 0.06, 0.12, 0.07, 0.07, 0.07, 0.06, 0.05, 0.06, 0.1, 0.06,
      0.08,
    ],
    interestExpense: [
      0.05, 0.06, 0.02, 0.04, 0.05, 0.08, 0.05, 0.05, 0.04, 0.04, 0.05, 0.07,
      0.02,
    ],
    healthInsuranceGA: [
      0.04, 0.04, 0.01, 0.04, 0.04, 0.05, 0.03, 0.05, 0.05, 0.04, 0.01, 0.03,
      0.03,
    ],
    salariesSales: [
      0.04, 0.02, 0.02, 0.04, 0.03, 0.06, 0.05, 0.04, 0.05, 0.03, 0.03, 0.03,
      0.04,
    ],
    buildingRepMaintenance: [
      0.002, 0.05, 0.02, 0.04, 0.07, 0.07, 0.01, 0.03, 0.02, 0.02, 0.03, 0.05,
      0.02,
    ],
    utilitiesExpense: [
      0.04, 0.04, 0.01, 0.05, 0.01, 0.04, 0.03, 0.04, 0.03, 0.04, 0.04, 0.04,
      0.05,
    ],
    other: [
      0.1, 0.06, 0.2, 0.1, 0.1, 0.06, 0.17, 0.14, 0.2, 0.11, 0.18, 0.17, 0.18,
    ],
  },

  // Wages vs Revenue data
  wagesRevenueData: {
    income: [3.4, 3.3, 2.6, 4.8, 6.0, 5.5, 3.1, 2.9, 2.7, 2.8, 5.2, 7.9, 5.2],
    salariesGA: [
      1.2, 1.4, 1.1, 1.3, 1.0, 1.2, 1.1, 1.0, 1.1, 1.0, 1.5, 1.6, 1.3,
    ],
    salariesSales: [
      0.6, 0.5, 0.4, 0.6, 0.5, 0.7, 0.6, 0.5, 0.6, 0.5, 0.5, 0.6, 0.6,
    ],
  },

  // Colors for charts
  colors: {
    roaRoe: ["#4a90e2", "#ff6b47"],
    expensesPie: [
      "#1f4e79",
      "#20b2aa",
      "#ff7f50",
      "#4db6ac",
      "#95a5a6",
      "#5d6d7e",
      "#bdc3c7",
      "#ffab91",
      "#9575cd",
      "#ba68c8",
      "#90a4ae",
    ],
    monthlyExpenses: [
      "#4a6fa5",
      "#2d5f5f",
      "#ff7f50",
      "#5f9ea0",
      "#8b7d82",
      "#4682b4",
      "#b0c4de",
      "#dda0dd",
      "#87ceeb",
      "#f0e68c",
      "#d3d3d3",
    ],
    wagesRevenue: ["#20b2aa", "#4a4a9a", "#ff7f50"],
  },

  // Text content and labels
  labels: {
    mainTitle: "Expense Summary",
    headerSubtext: "January 2025 | Acme Print",
    roaRoeTitle: "Return on Assets and Equity",
    expensesPieTitle: "Expenses: Top Accounts",
    monthlyExpensesTitle: "Expenses: Top Accounts Monthly",
    wagesRevenueTitle: "Expenses: Wages Vs Revenue Monthly",
    roaTitle: "Return on Assets",
    roeTitle: "Return on Equity",
    roaDescription:
      "Indicates how well Acme Print is using capital invested in Assets to generate Total Income. The higher the return, the more productive and efficient management is in utilizing economic resources the business has.",
    roeDescription:
      "Indicates how efficient company management is at generating growth from its Equity financing. Because Equity is equal to a company's Assets minus Liabilities, ROE is also considered the Return on Net Assets.",
  },
};

// operationalEfficiencyData.js
export const operationalEfficiencyData = {
  months: [
    "Jan 24",
    "Feb 24",
    "Mar 24",
    "Apr 24",
    "May 24",
    "Jun 24",
    "Jul 24",
    "Aug 24",
    "Sep 24",
    "Oct 24",
    "Nov 24",
    "Dec 24",
    "Jan 25",
  ],

  // Days Sales Outstanding data
  // daysSalesOutstanding: [55, 51, 59, 33, 35, 38, 59, 58, 53, 51, 29, 27, 46],

  // Days Payables Outstanding data
  daysPayablesOutstanding: [34, 29, 27, 25, 18, 27, 26, 14, 3, 1, 1, 10, 31],

  // Days Inventory Outstanding data
  daysInventoryOutstanding: [24, 25, 29, 22, 13, 16, 21, 20, 25, 20, 13, 9, 22],

  // Cash Conversion Cycle data
  cashConversionCycle: [51, 48, 62, 33, 30, 27, 54, 68, 75, 70, 41, 27, 37],

  // Fixed Asset Turnover data
  fixedAssetTurnover: [
    0.13, 0.12, 0.07, 0.23, 0.16, 0.24, 0.13, 0.13, 0.11, 0.08, 0.21, 0.4, 0.36,
  ],

  // Colors for charts
  colors: {
    salesOutstanding: "#2d6a9b",
    payablesOutstanding: "#565aa4",
    inventoryOutstanding: "#2a689a",
    cashConversion: "#ff6b47",
    fixedAssetTurnover: "#2d6a9b",
  },

  // Text content and labels
  labels: {
    mainTitle: "Operational Efficiency",
    headerSubtext: "January 2025 | Acme Print",
    daysSalesOutstandingTitle: "Days Sales (A/R) Outstanding",
    daysPayablesOutstandingTitle: "Days Payables (AP) Outstanding",
    daysInventoryOutstandingTitle: "Days Inventory Outstanding",
    cashConversionCycleTitle: "Cash Conversion Cycle",
    fixedAssetTurnoverTitle: "Fixed Asset Turnover",

    // Descriptions
    arApDescriptionTitle: "Days AR Outstanding & Days AP Outstanding",
    arApDescription:
      "Average number of days it takes customers to pay for invoices/ average number of days it takes a company to pay its suppliers.",

    cashConversionDescriptionTitle: "Cash Conversion Cycle (CCC)",
    cashConversionDescription:
      "The time it takes a company to convert the money spent on inventory or production back into cash by selling its goods or services. A shorter CCC is better because it means less time that money is tied up in inventory or accounts receivable.",

    fixedAssetTurnoverDescriptionTitle: "Fixed Asset Turnover (FAT)",
    fixedAssetTurnoverDescription:
      "The ratio of a company's net sales to its average fixed assets over a specific period, usually a year. A higher ratio indicates that a company is using its fixed assets more efficiently, while a lower ratio suggests underutilization.",
  },
};

// reportSummaryData.js
export const reportSummaryData = {
  // Main header information
  header: {
    mainTitle: "Report Summary",
    headerSubtext: "January 2025 | Acme Print",
  },

  // Executive Summary content
  executiveSummary: {
    title: "Executive Summary",
    content:
      "Acme Print has demonstrated significant financial improvement as of January 2025, reporting a notable increase in net profit compared to the previous year. The company achieved a YTD Net Profit of $1.7 million, with Total Income of $5.2 million, Cost of Goods Sold (COGS) at $2.3 million, and Total Expenses of $1.2 million. This led to a Net Profit Margin of 33% for January 2025, up from a loss in the previous year. This positive trend is driven by increased sales, improved cost management, and operational efficiency.",
  },

  // Analysis and Recommendations content
  analysisAndRecommendations: {
    title: "Analysis and Actionable Recommendations",

    sections: [
      {
        title: "Profitability Ratios",
        content: [
          "Net Profit Margin has increased 33% from a negative margin the previous year. This indicates improved profitability and cost management.",
          "Gross Profit Margin has risen to 56%, highlighting better control over production costs.",
        ],
        recommendation:
          "Maintain the current cost management strategies, particularly focusing on sustaining high-margin product lines and reducing labor inefficiencies.",
      },
      {
        title: "Revenue and Income Analysis",
        content: [
          {
            type: "paragraph",
            text: "Significant Sales Growth: Total income increased from $3.4 million in Jan 2024 to $5.2 million in Jan 2025.",
          },
          {
            type: "subsection",
            title: "Key Product Performance:",
            items: [
              "Product 05 - Top performer with sales of $1.63 million",
              "Freight Sales - Substantial growth to $1.31 million, up from $485K",
              "Packaging Sales - Increased to $633K from $295K",
            ],
          },
          {
            type: "subsection",
            title: "Underperforming Products",
            items: [
              "Product 02- Decreased sales from $72K to $6K.",
              "Product 04- Significant drop from $158K to $1K.",
            ],
          },
        ],
        recommendation:
          "Focus on promoting high-performing products (like Product 05) while reassessing the strategy for underperforming items, particularly Products 02 and 04.",
      },
      {
        title: "Cost Management and COGS Analysis",
        content: [
          "COGS as a percentage of income reduced from 65% to 44%.",
          {
            type: "subsection",
            title: "Cost Reductions:",
            items: [
              "Freight COGS - Increased by 21%, indicating higher volume of sales",
              "Labor COGS - Improved management with costs decreasing in several product lines",
              "Material Costs - Significant reductions in Product 04 and Product 05",
            ],
          },
        ],
        recommendation:
          "Continue focusing on labor efficiency and explore more strategic sourcing for raw materials to reduce material cost variations.",
      },
      {
        title: "Expense Management",
        content: [
          "Total expenses decreased by 14.5%, saving over $205K compared to the previous year.",
          {
            type: "subsection",
            title: "Major Reductions",
            items: [
              "Depreciation Expense- Reduced by $161K.",
              "Sales Commissions- Down by $136K, aligning commission structures with performance.",
            ],
          },
          {
            type: "subsection",
            title: "Areas of Increase",
            items: [
              "Rent Expense- Increased by 49.6K.",
              "Salaries - G&A- Up by $87K",
            ],
          },
        ],
        recommendation:
          "Investigate the rise in G&A salaries and rent. Consider evaluating office space utilization and exploring remote work options to mitigate rent costs.",
      },
      {
        title: "Operational Efficiency",
        content: [
          "Return on Assets (ROA) improved to 7%, indicating more efficient asset utilization.",
          "Return on Equity (ROE) improved to 12%, reflecting better management of equity resources.",
          "Quick Ratio improved to 1.48, indicating better short-term liquidity.",
        ],
        recommendation:
          "Maintain or enhance operational efficiency by investing in technology that maximizes asset utilization and minimizes downtime.",
      },
      {
        title: "Liquidity and Cash Flow",
        content: [
          "Quick Ratio improved to 1.48, indicating better short-term liquidity.",
          "Months of Cash on Hand increased to 2.19 months, reducing liquidity risk.",
          "Net Change in Cash was positive $1.4 million in January 2025.",
        ],
        recommendation:
          "Continue maintaining liquidity by preserving cash reserves and managing receivables more efficiently.",
      },
      {
        title: "Expense Breakdown and Recommendations",
        content: [
          {
            type: "subsection",
            title: "High-Impact Expenses",
            items: [
              "Depreciation- Despite reductions, still the largest single expense.",
              "Salaries - G&A- Continues to be a significant cost driver.",
            ],
          },
        ],
        recommendation: "Consider asset revaluation to reduce depreciation.",
        additionalRecommendation:
          "Optimize G&A expenses by streamlining administrative processes.",
      },
    ],
  },

  // Key Focus Areas content
  keyFocusAreas: {
    title: "Key Focus Areas for 2025",
    content:
      "Acme Print has demonstrated significant financial improvement, particularly in profitability and operational efficiency. Strategic cost management and increased sales have positively impacted the bottom line. Moving forward, focusing on optimizing underperforming products, managing rent and salary costs, and maintaining liquidity will ensure continued financial stability and growth.",
  },

  // Disclaimer text
  disclaimer:
    "The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.",
};

// tableOfContentsData.js
export const tableOfContentsData = {
  // Header information
  header: {
    mainTitle: "Table of Contents",
    headerSubtext: "January 2025 | Acme Print",
  },

  // Table of Contents items
  tocItems: [
    { text: "Report Summary", page: 1 },
    { text: "Current Fiscal Year", page: 4 },
    { text: "Profitability Ratios", page: 5 },
    { text: "Expense Summary", page: 6 },
    { text: "Operational Efficiency", page: 8 },
    { text: "Liquidity Summary", page: 10 },
    { text: "Profit & Loss - 13 Month Trailing", page: 11 },
    { text: "Profit & Loss - Monthly", page: 15 },
    { text: "Profit & Loss - YTD", page: 18 },
    { text: "Balance Sheet", page: 21 },
  ],
};

// YTD Table Data
export const YTDTableData = [
  // Income Section
  {
    category: "Income",
    isHeader: true,
  },
  {
    label: "Contracted Sales",
    indented: true,
    jan25: "$43",
    jan25Percent: "0.00%",
    jan24: "$21,633",
    jan24Percent: "0.64%",
    variance: "-$21,590",
    variancePercent: "0.64%",
    isNegative: true,
  },
  {
    label: "Sales Account",
    indented: true,
    jan25: "$55,795",
    jan25Percent: "1.07%",
    jan24: "$14,846",
    jan24Percent: "0.44%",
    variance: "$40,949",
    variancePercent: "0.63%",
    isPositive: true,
  },
  {
    label: "Sales Account - Freight",
    indented: true,
    jan25: "$1,310,258",
    jan25Percent: "25.03%",
    jan24: "$485,173",
    jan24Percent: "14.25%",
    variance: "$825,066",
    variancePercent: "10.75%",
    isPositive: true,
  },
  {
    label: "Sales Account - Packaging",
    indented: true,
    jan25: "$633,086",
    jan25Percent: "12.09%",
    jan24: "$295,101",
    jan24Percent: "8.69%",
    variance: "$337,985",
    variancePercent: "3.41%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 01",
    indented: true,
    jan25: "$24,712",
    jan25Percent: "0.47%",
    jan24: "$2,347",
    jan24Percent: "0.07%",
    variance: "$22,365",
    variancePercent: "0.40%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 02",
    indented: true,
    jan25: "$6,207",
    jan25Percent: "0.12%",
    jan24: "$22,404",
    jan24Percent: "2.10%",
    variance: "-$66,197",
    variancePercent: "-2.01%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 03",
    indented: true,
    jan25: "$226,703",
    jan25Percent: "4.33%",
    jan24: "$134,569",
    jan24Percent: "3.96%",
    variance: "$92,135",
    variancePercent: "0.37%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 04",
    indented: true,
    jan25: "$1,997",
    jan25Percent: "0.02%",
    jan24: "$158,077",
    jan24Percent: "4.65%",
    variance: "-$156,080",
    variancePercent: "-4.63%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 05",
    indented: true,
    jan25: "$1,630,413",
    jan25Percent: "31.15%",
    jan24: "$864,942",
    jan24Percent: "25.45%",
    variance: "$765,472",
    variancePercent: "5.70%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 06",
    indented: true,
    jan25: "$469,632",
    jan25Percent: "8.97%",
    jan24: "$295,049",
    jan24Percent: "8.68%",
    variance: "$174,603",
    variancePercent: "0.29%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 07",
    indented: true,
    jan25: "$201,552",
    jan25Percent: "3.85%",
    jan24: "$195,061",
    jan24Percent: "5.74%",
    variance: "$6,491",
    variancePercent: "-1.89%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 08",
    indented: true,
    jan25: "$277,516",
    jan25Percent: "5.30%",
    jan24: "$266,322",
    jan24Percent: "7.85%",
    variance: "$10,594",
    variancePercent: "-2.55%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 09",
    indented: true,
    jan25: "$87,173",
    jan25Percent: "1.67%",
    jan24: "$93,184",
    jan24Percent: "2.74%",
    variance: "-$6,011",
    variancePercent: "-1.08%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 10",
    indented: true,
    jan25: "$76,876",
    jan25Percent: "1.47%",
    jan24: "$45,181",
    jan24Percent: "1.33%",
    variance: "$31,695",
    variancePercent: "0.14%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 11",
    indented: true,
    jan25: "$161,148",
    jan25Percent: "3.08%",
    jan24: "$392,040",
    jan24Percent: "11.54%",
    variance: "-$230,892",
    variancePercent: "-8.46%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 12",
    indented: true,
    jan25: "$79,517",
    jan25Percent: "1.52%",
    jan24: "$66,238",
    jan24Percent: "1.95%",
    variance: "$13,279",
    variancePercent: "-0.43%",
    isPositive: true,
  },
  {
    label: "Total Sales Account",
    isTotal: true,
    jan25: "$5,241,687",
    jan25Percent: "100.14%",
    jan24: "$3,379,215",
    jan24Percent: "99.44%",
    variance: "$1,862,471",
    variancePercent: "0.70%",
    isPositive: true,
  },
  {
    label: "Sales Returns and Allowances",
    indented: true,
    jan25: "$0",
    jan25Percent: "0.00%",
    jan24: "$41",
    jan24Percent: "0.00%",
    variance: "-$41",
    variancePercent: "0.00%",
    isNegative: true,
  },
  {
    label: "Subcontractor Reimbursed",
    indented: true,
    jan25: "$7,252",
    jan25Percent: "-0.14%",
    jan24: "$9,425",
    jan24Percent: "-0.07%",
    variance: "$4,867",
    variancePercent: "0.07%",
    isPositive: true,
  },
  {
    label: "Total Income",
    isTotal: true,
    jan25: "$5,234,438",
    jan25Percent: "100.00%",
    jan24: "$3,398,350",
    jan24Percent: "100.00%",
    variance: "$1,836,058",
    variancePercent: "0.00%",
    isPositive: true,
  },
  // Cost of Goods Sold Section
  {
    category: "Cost of Goods Sold",
    isHeader: true,
  },
  {
    label: "Overhead (Cost of Sales)",
    indented: true,
    jan25: "$673,983",
    jan25Percent: "12.88%",
    jan24: "$555,256",
    jan24Percent: "16.34%",
    variance: "$118,727",
    variancePercent: "-3.46%",
    isPositive: true,
  },
  {
    label: "COGS Labor",
    indented: true,
    jan25: "$158,051",
    jan25Percent: "3.02%",
    jan24: "$51,238",
    jan24Percent: "1.51%",
    variance: "$106,812",
    variancePercent: "1.51%",
    isPositive: true,
  },
  {
    label: "COGS Labor - Freight",
    indented: true,
    jan25: "$3,420",
    jan25Percent: "0.07%",
    jan24: "$10,729",
    jan24Percent: "0.32%",
    variance: "-$7,309",
    variancePercent: "-0.25%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Packaging",
    indented: true,
    jan25: "$220,006",
    jan25Percent: "4.20%",
    jan24: "$140,882",
    jan24Percent: "4.15%",
    variance: "$79,222",
    variancePercent: "0.07%",
    isPositive: true,
  },
  {
    label: "COGS Labor - Pre-Press",
    indented: true,
    jan25: "$0",
    jan25Percent: "0.00%",
    jan24: "$3,370",
    jan24Percent: "0.10%",
    variance: "-$3,370",
    variancePercent: "-0.10%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 01",
    indented: true,
    jan25: "$1,397",
    jan25Percent: "0.03%",
    jan24: "$2,115",
    jan24Percent: "0.06%",
    variance: "-$622",
    variancePercent: "-0.04%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 02",
    indented: true,
    jan25: "$120",
    jan25Percent: "0.00%",
    jan24: "$218",
    jan24Percent: "0.01%",
    variance: "-$98",
    variancePercent: "0.00%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 03",
    indented: true,
    jan25: "$6,243",
    jan25Percent: "0.12%",
    jan24: "$7,859",
    jan24Percent: "0.23%",
    variance: "-$1,616",
    variancePercent: "-0.11%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 04",
    indented: true,
    jan25: "$4,731",
    jan25Percent: "0.09%",
    jan24: "$8,185",
    jan24Percent: "0.24%",
    variance: "-$3,454",
    variancePercent: "-0.15%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 05",
    indented: true,
    jan25: "$54,006",
    jan25Percent: "1.03%",
    jan24: "$60,618",
    jan24Percent: "1.78%",
    variance: "-$6,612",
    variancePercent: "-0.75%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 06",
    indented: true,
    jan25: "$86,924",
    jan25Percent: "1.66%",
    jan24: "$155,778",
    jan24Percent: "4.58%",
    variance: "-$68,854",
    variancePercent: "-2.92%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 07",
    indented: true,
    jan25: "$39,984",
    jan25Percent: "0.76%",
    jan24: "$39,045",
    jan24Percent: "2.30%",
    variance: "$40,061",
    variancePercent: "-1.59%",
    isPositive: true,
  },
  {
    label: "COGS Labor - Product 08",
    indented: true,
    jan25: "$115,516",
    jan25Percent: "2.21%",
    jan24: "$69,302",
    jan24Percent: "2.04%",
    variance: "$46,214",
    variancePercent: "0.17%",
    isPositive: true,
  },
  {
    label: "COGS Labor - Product 09",
    indented: true,
    jan25: "$24,216",
    jan25Percent: "0.46%",
    jan24: "$20,164",
    jan24Percent: "0.59%",
    variance: "$4,052",
    variancePercent: "-0.13%",
    isPositive: true,
  },
  {
    label: "COGS Labor - Product 10",
    indented: true,
    jan25: "$22,977",
    jan25Percent: "0.44%",
    jan24: "$70,780",
    jan24Percent: "2.08%",
    variance: "-$47,803",
    variancePercent: "-1.64%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 11",
    indented: true,
    jan25: "$14,339",
    jan25Percent: "0.27%",
    jan24: "$53,797",
    jan24Percent: "1.58%",
    variance: "-$39,459",
    variancePercent: "-1.31%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 12",
    indented: true,
    jan25: "$5,884",
    jan25Percent: "0.11%",
    jan24: "$3,075",
    jan24Percent: "0.09%",
    variance: "$2,808",
    variancePercent: "0.02%",
    isPositive: true,
  },
  {
    label: "Total COGS Labor",
    isTotal: true,
    jan25: "$755,819",
    jan25Percent: "14.44%",
    jan24: "$737,758",
    jan24Percent: "21.71%",
    variance: "$18,152",
    variancePercent: "-7.27%",
    isPositive: true,
  },
  {
    label: "COGS Materials",
    indented: true,
    jan25: "$372",
    jan25Percent: "0.01%",
    jan24: "$159",
    jan24Percent: "0.01%",
    variance: "$87",
    variancePercent: "0.01%",
    isPositive: true,
  },
  {
    label: "COGS Materials - Freight",
    indented: true,
    jan25: "$101",
    jan25Percent: "0.00%",
    jan24: "$115",
    jan24Percent: "0.00%",
    variance: "-$14",
    variancePercent: "0.00%",
    isNegative: true,
  },
  {
    label: "COGS Materials - Packaging",
    indented: true,
    jan25: "$136,995",
    jan25Percent: "2.62%",
    jan24: "$137,411",
    jan24Percent: "4.04%",
    variance: "-$415",
    variancePercent: "-1.43%",
    isNegative: true,
  },
  {
    label: "COGS Materials - Product 01",
    indented: true,
    jan25: "$41,011",
    jan25Percent: "0.78%",
    jan24: "$31,398",
    jan24Percent: "0.92%",
    variance: "$9,613",
    variancePercent: "-0.14%",
    isPositive: true,
  },
  {
    label: "COGS Materials - Product 02",
    indented: true,
    jan25: "$46,638",
    jan25Percent: "0.89%",
    jan24: "$83,704",
    jan24Percent: "2.46%",
    variance: "-$37,066",
    variancePercent: "-1.57%",
    isNegative: true,
  },
  {
    label: "COGS Materials - Product 03",
    indented: true,
    jan25: "$22,942",
    jan25Percent: "0.44%",
    jan24: "$64,037",
    jan24Percent: "1.88%",
    variance: "-$41,095",
    variancePercent: "-1.45%",
    isNegative: true,
  },
];

export const balanceSheetTableData = [
  // Assets Section
  {
    category: "Assets",
    isMainCategory: true,
  },
  {
    category: "Current Assets",
    isSubCategory: true,
  },
  {
    category: "Bank Accounts",
    isAccountGroup: true,
  },
  {
    label: "Bank 1",
    isAccount: true,
    jan25: "$180,344",
    jan25Percent: "$1,149,800",
    jan24: "$1,330,235",
    jan24Percent: "$39,153",
    variance: "$219,497",
    variancePercent: "1.30%",
  },
  {
    label: "Cash Drawer",
    isAccount: true,
    jan25: "$12",
    jan25Percent: "$512",
    jan24: "$1",
    jan24Percent: "$12",
    variance: "$21",
    variancePercent: "1.30%",
  },
  {
    label: "Total Bank Accounts",
    isTotal: true,
    jan25: "$179,832",
    jan25Percent: "$1,150,483",
    jan24: "$1,330,235",
    jan24Percent: "$39,647",
    variance: "$219,518",
    variancePercent: "1.30%",
  },
  {
    category: "Accounts Receivable",
    isAccountGroup: true,
  },
  {
    label: "Receivables",
    isAccount: true,
    jan25: "$6,881,986",
    jan25Percent: "$6,042,552",
    jan24: "$81,502",
    jan24Percent: "$6,173,885",
    variance: "$1,405,910",
    variancePercent: "1.30%",
  },
  {
    label: "Total Accounts Receivable",
    isTotal: true,
    jan25: "$6,881,904",
    jan25Percent: "$6,029,452",
    jan24: "$854,352",
    jan24Percent: "$6,375,889",
    variance: "$1,696,085",
    variancePercent: "1.30%",
  },
  {
    category: "Inventory",
    isAccountGroup: true,
  },
  {
    label: "Inventory - Asset",
    isAccount: true,
    jan25: "$6,507",
    jan25Percent: "$6,441",
    jan24: "$66",
    jan24Percent: "$6,310",
    variance: "$197",
    variancePercent: "1.30%",
  },
  {
    label: "Inventory - Materials",
    isAccount: true,
    jan25: "$1,491,933",
    jan25Percent: "$1,691,119",
    jan24: "$279,007",
    jan24Percent: "$1,547,253",
    variance: "$55,360",
    variancePercent: "1.30%",
  },
  {
    label: "Inventory - WIP",
    isAccount: true,
    jan25: "$106,119",
    jan25Percent: "$7,710",
    jan24: "$179,001",
    jan24Percent: "$146,753",
    variance: "$41,366",
    variancePercent: "1.30%",
  },
  {
    label: "Total Inventory",
    isTotal: true,
    jan25: "$1,604,559",
    jan25Percent: "$1,705,870",
    jan24: "$100,137",
    jan24Percent: "$1,700,316",
    variance: "$6,923",
    variancePercent: "1.30%",
  },
  {
    category: "Other Current Assets",
    isAccountGroup: true,
  },
  {
    label: "Allowance for Doubtful Account",
    isAccount: true,
    jan25: "$19,570",
    jan25Percent: "$7,280",
    jan24: "$12,290",
    jan24Percent: "$17,280",
    variance: "$2,290",
    variancePercent: "1.30%",
  },
  {
    label: "Deposits",
    isAccount: true,
    jan25: "$3,160",
    jan25Percent: "$3,040",
    jan24: "$0",
    jan24Percent: "$3,320",
    variance: "$160",
    variancePercent: "1.30%",
  },
  {
    label: "Employee Advances",
    isAccount: true,
    jan25: "$0",
    jan25Percent: "$0",
    jan24: "$0",
    jan24Percent: "$0",
    variance: "$0",
    variancePercent: "0%",
  },
  {
    label: "Prepaid Expenses",
    isAccount: true,
    jan25: "$300,787",
    jan25Percent: "$368,150",
    jan24: "$7,363",
    jan24Percent: "$371,832",
    variance: "$71,045",
    variancePercent: "1.30%",
  },
  {
    label: "Unbilled Receivables",
    isAccount: true,
    jan25: "$6,992,420",
    jan25Percent: "$5,749,344",
    jan24: "$1,243,076",
    jan24Percent: "$6,635,540",
    variance: "$356,880",
    variancePercent: "1.30%",
  },
  {
    label: "Trade Receivable",
    isAccount: true,
    jan25: "$12,304",
    jan25Percent: "$6,620",
    jan24: "$5,685",
    jan24Percent: "$12,304",
    variance: "$0",
    variancePercent: "0.00%",
  },
  {
    label: "Intercompany Receivables",
    isAccount: true,
    jan25: "$284,246",
    jan25Percent: "$177,064",
    jan24: "$205,573",
    jan24Percent: "$607,317",
    variance: "$134,986",
    variancePercent: "1.30%",
  },
  {
    label: "Total Other Current Assets",
    isTotal: true,
    jan25: "$7,538,824",
    jan25Percent: "$8,205,572",
    jan24: "$1,353,312",
    jan24Percent: "$7,545,028",
    variance: "$6,204",
    variancePercent: "4.30%",
  },
  {
    label: "Total Current Assets",
    isTotal: true,
    jan25: "$15,404,335",
    jan25Percent: "$15,090,277",
    jan24: "$1,731,119",
    jan24Percent: "$17,780,945",
    variance: "$1,115,725",
    variancePercent: "1.30%",
  },
  {
    category: "Fixed Assets",
    isSubCategory: true,
  },
  {
    label: "FA - Automobiles",
    isAccount: true,
    jan25: "$178,607",
    jan25Percent: "$186,791",
    jan24: "$7,184",
    jan24Percent: "$178,015",
    variance: "$8,592",
    variancePercent: "1.30%",
  },
  {
    label: "FA - Computer and Fixtures",
    isAccount: true,
    jan25: "$47,262",
    jan25Percent: "$84,540",
    jan24: "$2,912",
    jan24Percent: "$64,013",
    variance: "$37,249",
    variancePercent: "1.30%",
  },
  {
    label: "FA - Intangibles",
    isAccount: true,
    jan25: "$2,040",
    jan25Percent: "$2,040",
    jan24: "$0",
    jan24Percent: "$2,040",
    variance: "$0",
    variancePercent: "1.30%",
  },
  {
    label: "FA - Leasehold Improvements",
    isAccount: true,
    jan25: "$1,548,203",
    jan25Percent: "$1,586,342",
    jan24: "$163,585",
    jan24Percent: "$1,542,965",
    variance: "$134,785",
    variancePercent: "1.30%",
  },
  {
    label: "FA - Office Equipment",
    isAccount: true,
    jan25: "$325,577",
    jan25Percent: "$325,577",
    jan24: "$0",
    jan24Percent: "$325,577",
    variance: "$0",
    variancePercent: "1.30%",
  },
  {
    label: "FA - Plant Equipment",
    isAccount: true,
    jan25: "$21,105,629",
    jan25Percent: "$19,122,814",
    jan24: "$1,982,785",
    jan24Percent: "$20,808,781",
    variance: "$296,878",
    variancePercent: "1.30%",
  },
  {
    label: "FA - Software",
    isAccount: true,
    jan25: "$106,379",
    jan25Percent: "$123,401",
    jan24: "$2,797",
    jan24Percent: "$94,205",
    variance: "$1,464",
    variancePercent: "1.30%",
  },
  {
    label: "Fixed Asset Clearing Account",
    isAccount: true,
    jan25: "$56,000",
    jan25Percent: "$0",
    jan24: "$56,000",
    jan24Percent: "$54,880",
    variance: "$1,120",
    variancePercent: "1.30%",
  },
  {
    label: "AD - Automobiles",
    isAccount: true,
    jan25: "$279,416",
    jan25Percent: "$244,515",
    jan24: "$34,647",
    jan24Percent: "$268,352",
    variance: "$1,190",
    variancePercent: "1.30%",
  },
  {
    label: "AD - Computer Equipment",
    isAccount: true,
    jan25: "$547,879",
    jan25Percent: "$1,217,088",
    jan24: "$169,525",
    jan24Percent: "$1,509,581",
    variance: "$67,507",
    variancePercent: "1.30%",
  },
  {
    label: "AD - Furniture and Fixtures",
    isAccount: true,
    jan25: "$474,639",
    jan25Percent: "$482,069",
    jan24: "$50,153",
    jan24Percent: "$521,909",
    variance: "$1,562",
    variancePercent: "1.30%",
  },
  {
    label: "AD - Intangibles",
    isAccount: true,
    jan25: "$1,620",
    jan25Percent: "$1,600",
    jan24: "$0",
    jan24Percent: "$1,620",
    variance: "$140",
    variancePercent: "1.30%",
  },
  {
    label: "AD - Office Equipment",
    isAccount: true,
    jan25: "$54,009",
    jan25Percent: "$54,531",
    jan24: "$522",
    jan24Percent: "$59,704",
    variance: "$2,695",
    variancePercent: "-3.30%",
    isNegative: true,
  },
  {
    label: "AD - Plant Equipment",
    isAccount: true,
    jan25: "$12,410,647",
    jan25Percent: "$10,637,920",
    jan24: "$1,772,727",
    jan24Percent: "$12,569,179",
    variance: "$240,222",
    variancePercent: "2.30%",
  },
  {
    label: "AD - Software",
    isAccount: true,
    jan25: "$136,360",
    jan25Percent: "$104,534",
    jan24: "$22,806",
    jan24Percent: "$119,386",
    variance: "$7,954",
    variancePercent: "2.30%",
  },
  {
    label: "Total Fixed Assets",
    isTotal: true,
    jan25: "$8,247,264",
    jan25Percent: "$8,625,135",
    jan24: "$777,871",
    jan24Percent: "$7,547,779",
    variance: "$699,485",
    variancePercent: "2.30%",
  },
  {
    category: "Other Assets",
    isSubCategory: true,
  },
  {
    label: "Note Rec",
    isAccount: true,
    jan25: "$0",
    jan25Percent: "$1,507",
    jan24: "$1,507",
    jan24Percent: "$0",
    variance: "$0",
    variancePercent: "0.30%",
  },
  {
    label: "Property Loss Recoverable 1",
    isAccount: true,
    jan25: "$1,566",
    jan25Percent: "$623,817",
    jan24: "$625,183",
    jan24Percent: "$611,085",
    variance: "$596,299",
    variancePercent: "-1.16%",
    isNegative: true,
  },
  {
    label: "Total Other Assets",
    isTotal: true,
    jan25: "$10,836",
    jan25Percent: "$622,310",
    jan24: "$600,654",
    jan24Percent: "$611,585",
    variance: "$595,929",
    variancePercent: "2.30%",
  },
  {
    label: "Total Assets",
    isGrandTotal: true,
    jan25: "$24,110,275",
    jan25Percent: "$21,737,681",
    jan24: "$2,372,593",
    jan24Percent: "$26,210,324",
    variance: "$2,100,049",
    variancePercent: "2.30%",
  },
];

export const monthlyTableData = [
  // Income Section
  {
    category: "Income",
    isHeader: true,
  },
  {
    label: "Contracted Sales",
    indented: true,
    jan25: "$41",
    jan25Percent: "0%",
    jan24: "$21,633",
    jan24Percent: "1%",
    variance: "-$21,590",
    variancePercent: "-100%",
    isNegative: true,
  },
  {
    label: "Sales Account",
    indented: true,
    jan25: "$55,795",
    jan25Percent: "1%",
    jan24: "$14,846",
    jan24Percent: "0%",
    variance: "$40,949",
    variancePercent: "36%",
    isPositive: true,
  },
  {
    label: "Sales Account - Freight",
    indented: true,
    jan25: "$1,310,258",
    jan25Percent: "25%",
    jan24: "$445,173",
    jan24Percent: "14%",
    variance: "$865,085",
    variancePercent: "59%",
    isPositive: true,
  },
  {
    label: "Sales Account - Packaging",
    indented: true,
    jan25: "$633,086",
    jan25Percent: "12%",
    jan24: "$295,101",
    jan24Percent: "9%",
    variance: "$337,985",
    variancePercent: "87%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 01",
    indented: true,
    jan25: "$24,712",
    jan25Percent: "0%",
    jan24: "$2,347",
    jan24Percent: "0%",
    variance: "$22,365",
    variancePercent: "1%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 02",
    indented: true,
    jan25: "$6,207",
    jan25Percent: "0%",
    jan24: "$22,404",
    jan24Percent: "2%",
    variance: "-$66,197",
    variancePercent: "109%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 03",
    indented: true,
    jan25: "$226,703",
    jan25Percent: "4%",
    jan24: "$134,569",
    jan24Percent: "4%",
    variance: "$92,135",
    variancePercent: "146%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 04",
    indented: true,
    jan25: "$1,997",
    jan25Percent: "0%",
    jan24: "$158,077",
    jan24Percent: "5%",
    variance: "-$156,080",
    variancePercent: "101%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 05",
    indented: true,
    jan25: "$1,630,413",
    jan25Percent: "31%",
    jan24: "$864,942",
    jan24Percent: "25%",
    variance: "$765,472",
    variancePercent: "113%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 06",
    indented: true,
    jan25: "$469,632",
    jan25Percent: "9%",
    jan24: "$294,049",
    jan24Percent: "8%",
    variance: "$175,583",
    variancePercent: "160%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 07",
    indented: true,
    jan25: "$201,552",
    jan25Percent: "4%",
    jan24: "$195,061",
    jan24Percent: "6%",
    variance: "$6,491",
    variancePercent: "303%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 08",
    indented: true,
    jan25: "$277,516",
    jan25Percent: "5%",
    jan24: "$266,322",
    jan24Percent: "8%",
    variance: "$10,594",
    variancePercent: "291%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 09",
    indented: true,
    jan25: "$87,173",
    jan25Percent: "2%",
    jan24: "$93,184",
    jan24Percent: "3%",
    variance: "-$6,011",
    variancePercent: "150%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 10",
    indented: true,
    jan25: "$76,876",
    jan25Percent: "1%",
    jan24: "$45,181",
    jan24Percent: "1%",
    variance: "$31,695",
    variancePercent: "143%",
    isPositive: true,
  },
  {
    label: "Sales Account - Product 11",
    indented: true,
    jan25: "$161,148",
    jan25Percent: "3%",
    jan24: "$392,040",
    jan24Percent: "12%",
    variance: "-$230,892",
    variancePercent: "170%",
    isNegative: true,
  },
  {
    label: "Sales Account - Product 12",
    indented: true,
    jan25: "$79,517",
    jan25Percent: "2%",
    jan24: "$66,238",
    jan24Percent: "2%",
    variance: "$15,279",
    variancePercent: "240%",
    isPositive: true,
  },
  {
    label: "Total Sales Account",
    isTotal: true,
    jan25: "$5,241,687",
    jan25Percent: "100%",
    jan24: "$3,379,219",
    jan24Percent: "99%",
    variance: "$1,862,473",
    variancePercent: "181%",
    isPositive: true,
  },
  {
    label: "Sales Returns and Allowances",
    indented: true,
    jan25: "$0",
    jan25Percent: "0%",
    jan24: "$41",
    jan24Percent: "0%",
    variance: "-$41",
    variancePercent: "100%",
    isNegative: true,
  },
  {
    label: "Subcontractor Reimbursed",
    indented: true,
    jan25: "$7,252",
    jan25Percent: "0%",
    jan24: "$5,325",
    jan24Percent: "0%",
    variance: "$4,867",
    variancePercent: "150%",
    isPositive: true,
  },
  {
    label: "Total Income",
    isTotal: true,
    jan25: "$5,234,438",
    jan25Percent: "100%",
    jan24: "$3,398,380",
    jan24Percent: "100%",
    variance: "$1,836,058",
    variancePercent: "183%",
    isPositive: true,
  },
  // Cost of Goods Sold Section
  {
    category: "Cost of Goods Sold",
    isHeader: true,
  },
  {
    label: "COGS Freight (Cost of Sales)",
    indented: true,
    jan25: "$673,983",
    jan25Percent: "13%",
    jan24: "$555,256",
    jan24Percent: "16%",
    variance: "$118,727",
    variancePercent: "468%",
    isPositive: true,
  },
  {
    label: "COGS Labor",
    indented: true,
    jan25: "$158,051",
    jan25Percent: "3%",
    jan24: "$51,238",
    jan24Percent: "2%",
    variance: "$106,812",
    variancePercent: "48%",
    isPositive: true,
  },
  {
    label: "COGS Labor - Freight",
    indented: true,
    jan25: "$3,420",
    jan25Percent: "0%",
    jan24: "$10,729",
    jan24Percent: "0%",
    variance: "-$7,309",
    variancePercent: "147%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Packaging",
    indented: true,
    jan25: "$220,006",
    jan25Percent: "4%",
    jan24: "$140,882",
    jan24Percent: "4%",
    variance: "$79,522",
    variancePercent: "177%",
    isPositive: true,
  },
  {
    label: "COGS Labor - Pre-Press",
    indented: true,
    jan25: "$0",
    jan25Percent: "0%",
    jan24: "$3,370",
    jan24Percent: "0%",
    variance: "-$3,370",
    variancePercent: "100%",
    isNegative: true,
  },
  {
    label: "COGS Labor - Product 01",
    indented: true,
    jan25: "$1,397",
    jan25Percent: "0%",
    jan24: "$2,115",
    jan24Percent: "0%",
    variance: "-$622",
    variancePercent: "341%",
    isNegative: true,
  },
];

export const monthTrailingTableData = {
  income: [
    {
      category: "Income",
      isHeader: true,
      data: [],
    },
    {
      label: "Contracted Sales",
      indented: true,
      data: [
        "$80,200",
        "$17,500",
        "$77,890",
        "$95,000",
        "$105,826",
        "$57,324",
        "$78,651",
        "$31",
        "$41",
        "$43",
        "$36",
        "$332",
        "$41",
      ],
    },
    {
      label: "Sales Account",
      indented: true,
      data: [
        "$16,846",
        "$47,606",
        "$88,370",
        "$116,666",
        "$53,357",
        "$52,625",
        "$41,829",
        "$45,177",
        "$60,477",
        "$69,689",
        "$41,412",
        "$87,126",
        "$55,795",
      ],
    },
    {
      label: "Sales Account - Freight",
      indented: true,
      data: [
        "$45,178",
        "$50,561",
        "$23,540",
        "$73,306",
        "$40,141",
        "$93,100",
        "$105,060",
        "$59,183",
        "$78,136",
        "$50,862",
        "$87,008",
        "$116,239",
        "$110,528",
      ],
    },
    {
      label: "Sales Account - Packaging",
      indented: true,
      data: [
        "$295,101",
        "$238,966",
        "$370,319",
        "$510,353",
        "$216,354",
        "$541,626",
        "$318,474",
        "$308,751",
        "$314,830",
        "$448,494",
        "$624,537",
        "$785,800",
        "$633,086",
      ],
    },
    {
      label: "Sales Account - Product 01",
      indented: true,
      data: [
        "$2,347",
        "$65,545",
        "$38,321",
        "$86,566",
        "$84,210",
        "$33,557",
        "$39,183",
        "$51,240",
        "$61,038",
        "$50,740",
        "$76,009",
        "$79,942",
        "$24,712",
      ],
    },
    {
      label: "Sales Account - Product 02",
      indented: true,
      data: [
        "$2,742",
        "$4,295",
        "$11,862",
        "$8,565",
        "$2,810",
        "$15,757",
        "$7,515",
        "$7,140",
        "$4,128",
        "$3,350",
        "$1,980",
        "$8,250",
        "$3,620",
      ],
    },
    {
      label: "Sales Account - Product 03",
      indented: true,
      data: [
        "$134,569",
        "$152,338",
        "$81,018",
        "$189,541",
        "$340,486",
        "$438,190",
        "$370,171",
        "$334,564",
        "$381,243",
        "$372,399",
        "$327,038",
        "$444,379",
        "$226,707",
      ],
    },
    {
      label: "Sales Account - Product 04",
      indented: true,
      data: [
        "$130,077",
        "$12,664",
        "$58,541",
        "$198,622",
        "$381,444",
        "$128,523",
        "$50,235",
        "$26,966",
        "$47,502",
        "$8,541",
        "$39,674",
        "$46,838",
        "$1,997",
      ],
    },
    {
      label: "Sales Account - Product 05",
      indented: true,
      data: [
        "$864,942",
        "$380,623",
        "$903,405",
        "$1,259,703",
        "$1,954,372",
        "$1,667,963",
        "$789,560",
        "$815,196",
        "$621,706",
        "$707,126",
        "$1,288,302",
        "$2,390,396",
        "$1,630,413",
      ],
    },
    {
      label: "Sales Account - Product 06",
      indented: true,
      data: [
        "$294,049",
        "$412,841",
        "$189,564",
        "$461,597",
        "$385,042",
        "$654,067",
        "$513,148",
        "$312,399",
        "$390,151",
        "$569,624",
        "$456,937",
        "$508,170",
        "$469,632",
      ],
    },
    {
      label: "Sales Account - Product 07",
      indented: true,
      data: [
        "$195,061",
        "$41,692",
        "$106,622",
        "$329,356",
        "$427,841",
        "$388,073",
        "$348,031",
        "$148,768",
        "$151,248",
        "$141,949",
        "$229,815",
        "$257,896",
        "$201,552",
      ],
    },
    {
      label: "Sales Account - Product 08",
      indented: true,
      data: [
        "$266,322",
        "$113,223",
        "$45,023",
        "$209,132",
        "$545,568",
        "$710,505",
        "$421,607",
        "$142,418",
        "$107,125",
        "$178,664",
        "$349,918",
        "$412,380",
        "$277,518",
      ],
    },
    {
      label: "Sales Account - Product 09",
      indented: true,
      data: [
        "$24,847",
        "$3,157",
        "$2,541",
        "$20,725",
        "$34,585",
        "$22,650",
        "$24,607",
        "$9,214",
        "$24,418",
        "$30,464",
        "$42,339",
        "$45,146",
        "$30,314",
      ],
    },
    {
      label: "Sales Account - Product 10",
      indented: true,
      data: [
        "$45,181",
        "$119,464",
        "$8,412",
        "$54,769",
        "$54,589",
        "$32,649",
        "$51,891",
        "$65,858",
        "$38,491",
        "$62,076",
        "$122,854",
        "$191,416",
        "$76,876",
      ],
    },
    {
      label: "Sales Account - Product 11",
      indented: true,
      data: [
        "$309,449",
        "$152,318",
        "$153,169",
        "$255,472",
        "$376,653",
        "$443,718",
        "$562,315",
        "$463,231",
        "$423,488",
        "$359,059",
        "$483,644",
        "$755,907",
        "$161,148",
      ],
    },
    {
      label: "Sales Account - Product 12",
      indented: true,
      data: [
        "$64,238",
        "$1,737",
        "$73,841",
        "$14,626",
        "$86,212",
        "$54,685",
        "$35,923",
        "$39,552",
        "$40,468",
        "$25,074",
        "$75,801",
        "$104,561",
        "$79,517",
      ],
    },
    {
      label: "Total Sales Account",
      isTotal: true,
      data: [
        "$3,379,219",
        "$2,581,014",
        "$2,660,644",
        "$4,342,181",
        "$6,084,070",
        "$5,904,229",
        "$4,113,261",
        "$2,935,304",
        "$2,697,533",
        "$2,776,947",
        "$5,205,846",
        "$7,294,822",
        "$5,234,836",
      ],
    },
    {
      label: "Sales Returns and Allowances",
      indented: true,
      data: [
        "$41",
        "$280",
        "$3,381",
        "$680",
        "$196",
        "$98",
        "$0",
        "$0",
        "$0",
        "$0",
        "$0",
        "$0",
        "$0",
      ],
    },
    {
      label: "Subcontracting Reimbursed",
      indented: true,
      data: [
        "$5,340",
        "$107",
        "$1,646",
        "$17,948",
        "$14,207",
        "$11,717",
        "$1,781",
        "$13,156",
        "$5,565",
        "$3,531",
        "$16,094",
        "$32,008",
        "$7,232",
      ],
    },
    {
      label: "Total Income",
      isTotal: true,
      data: [
        "$3,390,380",
        "$2,582,042",
        "$2,664,814",
        "$4,362,194",
        "$6,094,822",
        "$5,923,139",
        "$4,131,541",
        "$2,935,304",
        "$2,697,533",
        "$2,776,947",
        "$5,205,846",
        "$7,294,822",
        "$5,234,836",
      ],
    },
  ],
  cogs: [
    {
      category: "Cost of Goods Sold",
      isHeader: true,
      data: [],
    },
    {
      label: "Overhead (Cost of Sales)",
      indented: true,
      data: [
        "$530,236",
        "$734,502",
        "$375,341",
        "$756,444",
        "$729,125",
        "$485,950",
        "$562,145",
        "$562,771",
        "$402,680",
        "$400,726",
        "$701,869",
        "$1,139,790",
        "$673,783",
      ],
    },
    {
      label: "COGS Labor",
      indented: true,
      data: [
        "$51,238",
        "$119,631",
        "$59,449",
        "$59,997",
        "$120,865",
        "$107,573",
        "$84,948",
        "$79,530",
        "$52,284",
        "$48,417",
        "$112,482",
        "$65,512",
        "$158,055",
      ],
    },
  ],
};

//fiscal year data
export const fiscalYearData = {
  // Header Information
  header: {
    title: "Current Fiscal Year",
    subtitle: "January 2025 | Acme Print",
  },

  // YTD Fiscal Metrics
  fiscalMetrics: [
    {
      label: "YTD Total Income",
      value: "$5.2m",
      highlighted: false,
    },
    {
      label: "YTD Cost of Goods Sold",
      value: "$2.3m",
      highlighted: true,
    },
    {
      label: "YTD Total Expense",
      value: "$1.2m",
      highlighted: false,
    },
    {
      label: "YTD Net Profit",
      value: "$1.7m",
      highlighted: true,
    },
  ],

  // Chart Data
  charts: {
    // Common categories for all charts
    categories: [
      "Jan 24",
      "Feb 24",
      "Mar 24",
      "Apr 24",
      "May 24",
      "Jun 24",
      "Jul 24",
      "Aug 24",
      "Sep 24",
      "Oct 24",
      "Nov 24",
      "Dec 24",
      "Jan 25",
    ],

    // Stacked Column Chart Data
    stackedColumn: {
      series: [
        {
          name: "Cost of Goods Sold",
          type: "column",
          data: [
            2.2, 2.0, 1.9, 2.4, 4.4, 3.0, 1.9, 1.9, 1.5, 2.0, 3.1, 4.8, 2.3,
          ],
        },
        {
          name: "Expense",
          type: "column",
          data: [
            1.4, 1.4, 1.2, 1.5, 1.3, 1.5, 1.1, 1.2, 1.1, 1.2, 1.6, 1.6, 1.2,
          ],
        },
        {
          name: "Income",
          type: "line",
          data: [
            3.4, 3.3, 2.6, 4.8, 6.0, 5.5, 3.1, 2.9, 2.7, 2.8, 5.2, 7.9, 5.2,
          ],
        },
      ],
    },

    // Net Income Raw Data
    netIncomeRaw: [-7, -6, -15, 19, 6, 0, 19, 2, -8, 2, -15, 10, 19, 33],

    // Gross Profit Margin Data
    grossProfitMargin: [35, 38, 30, 50, 28, 46, 38, 34, 43, 29, 41, 40, 56],
  },

  // Chart Sections Configuration
  chartSections: [
    {
      title: "Monthly Performance Breakdown",
      type: "stackedColumn",
    },
    {
      title: "Net Income/(Loss)",
      type: "netIncome",
    },
    {
      title: "Gross Profit Margin",
      type: "grossProfitMargin",
      description: {
        title: "Gross Profit Margin",
        content:
          "Is a share of Gross Profit in Total Income or the profit left for covering operating and other expenses. A good Gross Profit Margin is high enough to cover overhead and leave a reasonable Net Profit.",
      },
    },
    {
      title: "Net Profit Margin",
      type: "netProfitMargin",
      description: {
        title: "Net Profit Margin",
        content:
          "Shows the profit earned per dollar of income. A 10% Net Profit Margin is considered an excellent ratio. If your company has a low Net Profit Margin you are making very little profit after all costs. That implies the revenue is getting eaten up by expenses. It also increases the risk your firm will be unable to meet obligations. With a low margin, a sudden dip in sales over the next month or year could turn your company unprofitable. A high margin indicates your company has solid competitive advantages.",
      },
    },
  ],
};
