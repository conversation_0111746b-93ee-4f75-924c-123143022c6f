import axiosInstance from "./axiosInstance";

export const create = async (data) => {
    return await axiosInstance.post("/company", data);
};

export const update = async (companyId, data) => {
    return await axiosInstance.put(`/company/${companyId}`, data);
};

export const getAll = async (params) => {
    return await axiosInstance.get(`/company/all?page=${params.page}&pageSize=${params.pageSize}&search=${params.search}&sortBy=${params.sortBy || ''}&sortOrder=${params.sortOrder || ''}`)
}

export const getOneById = async (id) => {
    return await axiosInstance.get(`/company/${id}`)
}

export const deleteOne = async(companyId) => {
    return await axiosInstance.delete(`/company/${companyId}`)
}

export const uploadDoc = async(companyId, payload) => {
    return await axiosInstance.post(`/company/${companyId}/upload-file`, payload)
}

export const getCompanyDocs = async (id) => {
    return await axiosInstance.get(`/company/${id}`)
}




export const getAllSharedCompanies = async (params) => {
    return await axiosInstance.get(`/shared-companies`)
}

export const getSharedCompanyDetails = async (id) => {
    return await axiosInstance.get(`/shared-companies/${id}`)
}

export const addUser  = async (user) => {
    return await axiosInstance.post(`/shared-companies/add-user`, user)
}

export const deleteUser  = async (userId) => {
    return await axiosInstance.delete(`/shared-companies/delete-user/${userId}`)
}

