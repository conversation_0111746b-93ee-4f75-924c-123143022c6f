import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js";
const baseUrl = `/api/v1/templates`

describe('GET /api/templates', () => {


  it('should fetch all templates with signed URLs', async () => {
    const res = await request(app).get(`${baseUrl}/all`);

    expect(res._body.statusCode).to.equal(200);
    expect(res._body.success).to.be.true;
    expect(res._body.message).to.equal('Templates fetched successfully');
  });
  it('should handle errors gracefully', async () => {
    const res = await request(app).get(`${baseUrl}/all`);

    expect(res._body.statusCode).not.equal(500);
    expect(res._body.success).not.equal(false);
    expect(res.body.message).not.equal('Internal Server Error');
  });
});
