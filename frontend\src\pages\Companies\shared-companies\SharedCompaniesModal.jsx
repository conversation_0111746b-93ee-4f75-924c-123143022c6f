import React, { useState, useEffect } from "react";
import {
  Modal,
  Box,
  TextField,
  Button,
  List,
  ListItem,
  ListItemText,
  MenuItem,
  Select,
  IconButton,
  CircularProgress,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { addUserInSharedList } from "../../../services/settings";
import { useDebouncedSearch } from '../../../hooks/useDebounceSearch';

const SharedCompaniesModal = ({
  open,
  onClose,
  data,
  onAddUser,
  fetchUsers,
  isCreatedModal,
  isLoading,
}) => {
  const [search, setSearch] = useState("");
  const [page, setPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [addedUsers, setAddedUsers] = useState([]);
  const debouncedSearch = useDebouncedSearch(search);
  const navigate = useNavigate();
  const handleAddUser = async (user) => {
    try {
      const payload = { 
        shareWith: user?.id,
        username: user.username
      };
      await addUserInSharedList(payload);
      setAddedUsers(prev => [...prev, user.username]);
      setFilteredUsers(prev =>
        prev?.map(u => (u.id === user.id ? { ...u, added: true } : u))
      );
      onAddUser([user]);
    } catch (error) {
      console.error("Error adding user:", error);
    }
  };

  useEffect(() => {
    if (data?.users) {
      setFilteredUsers(
        data.users.map(u => ({
          ...u,
          added: addedUsers.includes(u.username),
        }))
      );
    }
  }, [data, addedUsers]);

  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= (data?.pagination?.totalPages || 1)) {
      setPage(newPage);
      fetchUsers(newPage, itemsPerPage, debouncedSearch);
    }
  };

  const handleItemsPerPageChange = (event) => {
    const newSize = Number(event.target.value);
    setItemsPerPage(newSize);
    fetchUsers(1, newSize, debouncedSearch);
    setPage(1);
  };

  const handleClose = () => {
    if (isCreatedModal) navigate("/share-companies");
    onClose();
    setItemsPerPage(10);
    setPage(1);
    setSearch("");
    setAddedUsers([]);
  };

  const handleSearchChange = (event) => {
    setSearch(event.target.value);
    setPage(1); 
  };

  useEffect(() => {
    if (open && fetchUsers) {
      fetchUsers(page, itemsPerPage, debouncedSearch);
    }
  }, [open, debouncedSearch, page, itemsPerPage, fetchUsers]);

  return (
    <Modal
      open={open}
      onClose={handleClose}
      sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}
    >
      <Box
        sx={{
          width: 508,
          height: "719px",
          overflow: "auto",
          bgcolor: "white",
          p: 3,
          borderRadius: 2,
          boxShadow: 24,
          position: "relative",
        }}
      >
        <IconButton
          onClick={handleClose}
          sx={{ position: "absolute", top: 8, right: 8 }}
        >
          <Close />
        </IconButton>

        <TextField
          fullWidth
          label="Search User"
          variant="outlined"
          value={search}
          onChange={handleSearchChange}
          sx={{ mb: 2, mt: 3 }}
        />
        
        {isLoading ? (
          <div className="flex justify-center items-center mt-[180px]">
            <CircularProgress />
          </div>
        ) : filteredUsers?.length > 0 ? (
          <>
            <List> 
              {filteredUsers?.map((user) => (
                <ListItem
                  key={user.id}
                  secondaryAction={
                    <Button
                      variant="contained"
                      onClick={() => handleAddUser(user)}
                      disabled={addedUsers.includes(user.username)}
                      sx={{ minWidth: 80 }}
                    >
                      {addedUsers.includes(user.username) ? "Added" : "Add"}
                    </Button>
                  }
                >
                  <ListItemText primary={user.username} />
                </ListItem>
              ))}
            </List>

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                mt: 2,
                alignItems: "center",
              }}
            >
              <Button
                disabled={page === 1}
                onClick={() => handlePageChange(page - 1)}
              >
                Previous
              </Button>
              <Select
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
              >
                <MenuItem value={10}>10</MenuItem>
                <MenuItem value={20}>20</MenuItem>
                <MenuItem value={50}>50</MenuItem>
              </Select>
              <Button
                disabled={page === data?.pagination?.totalPages}
                onClick={() => handlePageChange(page + 1)}
              >
                Next
              </Button>
            </Box>
          </>
        ) : (
          <div className="flex justify-center items-center mt-[180px] font-medium text-xl">
            No user
          </div>
        )}
      </Box>
    </Modal>
  );
};

export default SharedCompaniesModal;