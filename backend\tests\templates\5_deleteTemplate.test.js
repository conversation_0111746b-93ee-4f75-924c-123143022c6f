import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
import { customTemplate ,deleteCustomTemplate} from "../helpers/templateHelper.js";
const baseUrl = `/api/v1/templates`;

describe('DELETE /templates/:id', () => {
    let templateId
    before(async()=>{
     templateId =  await customTemplate()
    })
    after(async () => {
        try {
            await deleteCustomTemplate();  
            console.log("All test templates deleted successfully.");
        } catch (error) {
            console.error("Error deleting test templates:", error);
        }
    });

    it('should delete a template and associated documents', async () => {
       
        const res = await request(app).delete(`${baseUrl}/${templateId}`);        

        expect(res.status).to.equal(200);
        expect(res._body.success).to.be.true;
        expect(res._body.message).to.equal('Template and associated documents deleted successfully');
    });


    it('should return 404 if template not found', async () => {
    

        const res = await request(app).delete(`${baseUrl}/01`);
        

        expect(res._body.statusCode).to.equal(404);
        expect(res._body.message).to.equal('Template not found');
    });

    it('should handle errors during deletion', async () => {

        const res = await request(app).delete(`${baseUrl}/${templateId}`);

        expect(res._body.status).not.equal(500);
        expect(res._body.message).not.equal('Database error');
    });
});
