<!DOCTYPE html>
<html lang="en">
 
<head>
    <meta charset="UTF-8">
    <title>Net Income/(Loss) - Threshold Colored Line</title>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
 
        #chart {
            max-width: 900px;
            margin: auto;
        }
    </style>
</head>
 
<body>
 
    <h2>Net Income/(Loss) - Negative values with different color</h2>
    <div id="chart"></div>
 
    <script>
 
 
 
        var options = {
            series: [
                {
                    name: "High - 2013",
                    data: [28.8, 29, 33, 36.29, 32, 32, 33]
                },
                {
                    name: "Low - 2013",
                    data: [26.6, 11, 14, 36.46, 17, 13, 13]
                }
            ],
            chart: {
                height: 350,
                type: 'line',
                dropShadow: {
                    enabled: true,
                    color: '#000',
                    top: 18,
                    left: 7,
                    blur: 10,
                    opacity: 0.5
                },
                zoom: {
                    enabled: false
                },
                toolbar: {
                    show: false
                }
            },
            colors: ['#77B6EA', '#545454'],
            dataLabels: {
                enabled: true,
            },
            stroke: {
                curve: 'smooth'
            },
            title: {
                text: 'Average High & Low Temperature',
                align: 'left'
            },
            grid: {
                borderColor: '#e7e7e7',
                row: {
                    colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
                    opacity: 0.5
                },
            },
            markers: {
                size: 1
            },
            xaxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                title: {
                    text: 'Month'
                }
            },
            yaxis: {
                title: {
                    text: 'Temperature'
                },
                min: 5,
                max: 40
            },
            legend: {
                position: 'top',
                horizontalAlign: 'right',
                floating: true,
                offsetY: -25,
                offsetX: -5
            }
        };
 
        var chart = new ApexCharts(document.querySelector("#chart"), options);
        chart.render();
    </script>
</body>
 
</html>
 