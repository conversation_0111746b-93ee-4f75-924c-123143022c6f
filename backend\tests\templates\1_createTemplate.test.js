import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js";
const baseUrl = `/api/v1/company`


describe('POST /api/templates - createExampleTemplate',() => {

  it('should successfully create a template with uploaded files', async () => {
    const res = await request(app)
      .post('/api/v1/templates')
      .attach('files', Buffer.from('Test file content'), 'sample.pdf')
      .field('name', 'Test Template')
      .field('description', 'Template description');    

    expect(res._body.statusCode).to.equal(201);
    expect(res._body.success).to.be.true;
    expect(res._body.message).to.equal('Example created successfully');
    expect(res._body.body).to.have.property('name', 'Test Template');
  });

  it('should return 400 if no file is uploaded', async () => {
    const res = await request(app)
      .post('/api/v1/templates/')
      .field('name', 'Test Template')
      .field('description', 'Template description');    

    expect(res.status).to.equal(400);
    expect(res._body.message).to.equal('No file uploaded');
  });
  it('should handle multiple file uploads', async () => {
    const res = await request(app)
      .post('/api/v1/templates/')
      .attach('files', Buffer.from('File 1 content'), 'file1.pdf')
      .attach('files', Buffer.from('File 2 content'), 'file2.pdf')
      .field('name', 'Multi-File Template')
      .field('description', 'Multiple files upload test');
    
    expect(res._body.statusCode).to.equal(201);
    expect(res._body.success).to.be.true;
    expect(res._body.body.documents).to.have.lengthOf(2);
  });


  it('should return 500 if an internal error occurs', async () => {
    const res = await request(app)
      .post('/api/v1/templates/')
      .attach('files', Buffer.from('Test file content'), 'sample.pdf')
      .field('name',"Test Template" ) // Invalid input to trigger an error
      .field('description', 'Template description');
    
    expect(res._body.statusCode).not.equal(500);
    expect(res._body.success).not.equal(false);
  });
});
