import { <PERSON>rror<PERSON>and<PERSON> } from './errorHandler.js';

/**
 * Common error response handler
 * @param {Error} error - The error object
 * @param {Object} res - Express response object
 * @param {Object} req - Express request object
 * @param {string} context - Context for logging (e.g., 'QBO Connect', 'QBO Callback')
 * @param {Object} additionalData - Additional data to include in error response
 */
export const handleErrorResponse = (
  error,
  res,
  req,
  context = 'API',
  additionalData = {},
) => {
  // Log the full error for debugging
  console.error(`${context} Error:`, {
    message: error.message,
    stack: error.stack,
    statusCode: error.statusCode,
    userId: req.user?.id,
    companyId: req.query?.companyId || req.params?.companyId,
    url: req.originalUrl,
    method: req.method,
    timestamp: new Date().toISOString(),
    ...additionalData,
  });

  // Determine status code and error message
  let statusCode = 500;
  let errorMessage = 'An unexpected error occurred';

  if (error instanceof ErrorHandler) {
    statusCode = error.statusCode;
    errorMessage = error.message;
  } else {
    // Handle specific built-in error types
    switch (error.name) {
      case 'ValidationError':
        statusCode = 400;
        errorMessage = 'Invalid request data provided';
        break;
      case 'PrismaClientKnownRequestError':
        statusCode = 500;
        errorMessage = 'Database operation failed';
        break;
      case 'TimeoutError':
        statusCode = 408;
        errorMessage = 'Request timeout. Please try again';
        break;
      default:
        // Handle specific error codes
        switch (error.code) {
          case 'ETIMEDOUT':
            statusCode = 408;
            errorMessage = 'Request timeout. Please try again';
            break;
          case 'COMPANY_NOT_FOUND':
            statusCode = 404;
            errorMessage = 'Company not found';
            break;
          case 'INVALID_STATE':
            statusCode = 400;
            errorMessage = 'Invalid security state parameter';
            break;
          case 'AUTHORIZATION_DENIED':
            statusCode = 403;
            errorMessage = 'Authorization was denied';
            break;
          case 'QBO_NOT_CONNECTED':
            statusCode = 200;
            errorMessage = 'QuickBooks is not connected';
            break;
          default:
            errorMessage = error.message || errorMessage;
        }
    }
  }

  // Build error response
  const errorResponse = {
    success: false,
    message: errorMessage,
    ...additionalData,
  };

  return res.status(statusCode).json(errorResponse);
};

/**
 * Common success response handler
 * @param {Object} res - Express response object
 * @param {string} message - Success message
 * @param {Object} data - Data to return
 * @param {number} statusCode - HTTP status code (default: 200)
 */
export const handleSuccessResponse = (
  res,
  message,
  data = {},
  statusCode = 200,
) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
  });
};
