import React from 'react';

const BalanceSheetDashboard = ({ 
  headerTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  reportData = null // Add reportData prop to receive API data
}) => {
  // Extract background color from headerTextStyle for table header
  const headerBgColor = headerTextStyle.color || '#20b2aa';

  // Function to transform API data into the required format
  const transformApiData = (apiData) => {
    if (!apiData || !apiData.balanceSheetTableData) {
      return { 
        tableData: [], 
        dateInfo: { 
          current: 'Jan 25', 
          previousYear: 'Jan 24', 
          previousMonth: 'Dec 24' 
        } 
      };
    }

    const rawData = apiData.balanceSheetTableData;
    
    // Extract date information from the first item's keys
    let currentDate = 'Jan 25';
    let previousYear = 'Jan 24';
    let previousMonth = 'Dec 24';
    
    if (rawData.length > 0) {
      const keys = Object.keys(rawData[0]);
      
      // Extract current date
      const actualsKey = keys.find(key => key.includes('_Actuals'));
      if (actualsKey) {
        const dateMatch = actualsKey.match(/([A-Za-z]+_\d+)_Actuals/);
        if (dateMatch) {
          currentDate = dateMatch[1].replace('_', ' ');
          
          // Calculate previous year (same month, year-1)
          const [month, year] = currentDate.split(' ');
          previousYear = `${month} ${parseInt(year) - 1}`;
        }
      }
      
      // Extract previous month
      const priorMonthKey = keys.find(key => key.includes('_Prior_Month'));
      if (priorMonthKey) {
        const monthMatch = priorMonthKey.match(/([A-Za-z]+_\d+)_Prior_Month/);
        if (monthMatch) {
          previousMonth = monthMatch[1].replace('_', ' ');
        }
      }
    }

    // Group data by accountClassification and then by account_type
    const classificationGroups = rawData.reduce((acc, item) => {
      const classification = item.accountClassification;
      const accountType = item.account_type;
      
      if (!acc[classification]) {
        acc[classification] = {};
      }
      if (!acc[classification][accountType]) {
        acc[classification][accountType] = [];
      }
      acc[classification][accountType].push(item);
      return acc;
    }, {});

    // Transform grouped data into table format
    const tableData = [];
    
    Object.keys(classificationGroups).forEach(classification => {
      // Add main category header (Asset, Liability, Equity)
      tableData.push({
        isMainCategory: true,
        category: classification
      });

      const accountTypes = classificationGroups[classification];
      
      Object.keys(accountTypes).forEach(accountType => {
        // Add sub-category header (account type)
        tableData.push({
          isSubCategory: true,
          category: accountType
        });

        // Add individual account rows
        accountTypes[accountType].forEach(item => {
          // Check if this is a total row (contains "Total" in account_name)
          const isTotal = item.account_name.toLowerCase().includes('total');
          
          // Determine if variance is negative for styling
          const priorYearVariance = parseFloat(item.Variance_Prior_Year || '0');
          const priorMonthVariance = parseFloat(item.Variance_Prior_Month || '0');
          const isNegativePriorYear = priorYearVariance < 0;
          const isNegativePriorMonth = priorMonthVariance < 0;
          
          tableData.push({
            label: item.account_name,
            jan25: item.Apr_25_Actuals || '0', // Current period actuals
            jan25Percent: item.Apr_25_Actuals || '0', // Using actuals for percentage column
            jan24: item.Apr_24_Prior_Year || '0', // Prior year
            jan24Percent: item.Variance_Prior_Year || '0', // Prior year variance
            variance: item.Mar_25_Prior_Month || '0', // Prior month
            variancePercent: item.Variance_Prior_Month || '0', // Prior month variance
            isTotal: isTotal,
            isGrandTotal: item.account_name.toLowerCase().includes('total') && 
                         (classification.toLowerCase().includes('asset') || 
                          classification.toLowerCase().includes('total')),
            isNegative: isNegativePriorMonth // Use prior month variance for styling
          });
        });

        // Add total row for each account type if needed
        const totalRow = accountTypes[accountType].find(item => 
          item.account_name.toLowerCase().includes('total')
        );
        
        if (!totalRow && accountTypes[accountType].length > 1) {
          // Calculate totals if not provided in API
          const totals = accountTypes[accountType].reduce((sum, item) => {
            if (!item.account_name.toLowerCase().includes('total')) {
              sum.actuals += parseFloat(item.Apr_25_Actuals || '0');
              sum.priorYear += parseFloat(item.Apr_24_Prior_Year || '0');
              sum.priorMonth += parseFloat(item.Mar_25_Prior_Month || '0');
            }
            return sum;
          }, { actuals: 0, priorYear: 0, priorMonth: 0 });
          
          tableData.push({
            label: `Total ${accountType}`,
            jan25: totals.actuals.toString(),
            jan25Percent: totals.actuals.toString(),
            jan24: totals.priorYear.toString(),
            jan24Percent: (totals.actuals - totals.priorYear).toString(),
            variance: totals.priorMonth.toString(),
            variancePercent: (totals.actuals - totals.priorMonth).toString(),
            isTotal: true,
            isNegative: (totals.actuals - totals.priorMonth) < 0
          });
        }
      });
    });

    return { 
      tableData, 
      dateInfo: { 
        current: currentDate, 
        previousYear: previousYear, 
        previousMonth: previousMonth 
      } 
    };
  };

  // Transform the API data
  const { tableData, dateInfo } = transformApiData(reportData);

  // Fallback to empty state if no data
  if (!reportData || !reportData.balanceSheetTableData || tableData.length === 0) {
    return (
      <div className="min-h-screen p-5">
        <div className="max-w-6xl bg-white p-10 mx-auto overflow-x-auto shadow">
          <div>
            <h2 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}>
              Balance Sheet
            </h2>
            <div style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}>
              Balance Sheet Report
            </div>
            <div style={{ ...contentTextStyle, fontSize: "20px" }}>
              Acme Print
            </div>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-lg text-gray-600">No data available</div>
              <div className="text-sm text-gray-500 mt-2">Please check your data source</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderTableRow = (item, index) => {
    if (item.isMainCategory) {
      return (
        <tr key={index}>
          <td className="text-left pl-2 font-bold text-gray-800" style={contentTextStyle}>
            {item.category}
          </td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
        </tr>
      );
    }

    if (item.isSubCategory) {
      return (
        <tr key={index}>
          <td className="text-left pl-4 font-semibold text-gray-700" style={contentTextStyle}>
            {item.category}
          </td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
        </tr>
      );
    }

    if (item.isAccountGroup) {
      return (
        <tr key={index}>
          <td className="text-left pl-6 font-medium text-gray-600" style={contentTextStyle}>
            {item.category}
          </td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
          <td className="text-right">&nbsp;</td>
        </tr>
      );
    }

    if (item.isGrandTotal) {
      return (
        <tr key={index} className="border-t-2 border-gray ">
          <td className="text-left pl-2 font-bold text-gray-900" style={contentTextStyle}>
            <strong>{item.label}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={contentTextStyle}>
            <strong>{item.jan25}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={contentTextStyle}>
            <strong>{item.jan24}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={contentTextStyle}>
            <strong>{item.jan24Percent}</strong>
          </td>
          <td className="text-right font-mono font-bold" style={contentTextStyle}>
            <strong>{item.variance}</strong>
          </td>
          <td className="text-right font-bold" style={contentTextStyle}>
            <strong>{item.variancePercent}</strong>
          </td>
        </tr>
      );
    }

    if (item.isTotal) {
      return (
        <tr key={index} className="border-t-2 border-gray">
          <td className="text-left pl-6 font-semibold" style={contentTextStyle}>
            <strong>{item.label}</strong>
          </td>
          <td className="text-right font-mono" style={contentTextStyle}>
            <strong>{item.jan25}</strong>
          </td>
          <td className="text-right font-mono" style={contentTextStyle}>
            <strong>{item.jan24}</strong>
          </td>
          <td className="text-right font-mono" style={contentTextStyle}>
            <strong>{item.jan24Percent}</strong>
          </td>
          <td className="text-right font-mono" style={contentTextStyle}>
            <strong>{item.variance}</strong>
          </td>
          <td 
            className={`text-right ${item.isNegative ? 'text-red-600' : ''}`}
            style={contentTextStyle}
          >
            <strong>{item.variancePercent}</strong>
          </td>
        </tr>
      );
    }

    return (
      <tr key={index}>
        <td className="text-left pl-8 font-normal" style={contentTextStyle}>
          {item.label}
        </td>
        <td className="text-right font-mono" style={contentTextStyle}>
          {item.jan25Percent}
        </td>
        <td className="text-right font-mono" style={contentTextStyle}>
          {item.jan24}
        </td>
        <td className="text-right font-mono" style={contentTextStyle}>
          {item.jan24Percent}
        </td>
        <td className="text-right font-mono" style={contentTextStyle}>
          {item.variance}
        </td>
        <td 
          className={`text-right ${item.isNegative ? 'text-red-600' : ''}`}
          style={contentTextStyle}
        >
          {item.variancePercent}
        </td>
      </tr>
    );
  };

  // Generate dynamic date description
  const getDateDescription = () => {
    if (dateInfo.current) {
      const [month, year] = dateInfo.current.split(' ');
      const monthNames = {
        'Jan': 'January', 'Feb': 'February', 'Mar': 'March', 'Apr': 'April',
        'May': 'May', 'Jun': 'June', 'Jul': 'July', 'Aug': 'August',
        'Sep': 'September', 'Oct': 'October', 'Nov': 'November', 'Dec': 'December'
      };
      const fullMonth = monthNames[month] || month;
      return `As of ${fullMonth} 31st, 20${year}`;
    }
    return 'As of January 31st, 2025';
  };

  return (
    <div className="min-h-screen p-5">
      {/* Main Container */}
      <div className="max-w-6xl bg-white p-10 mx-auto overflow-x-auto shadow">
        
        {/* Header Section */}
        <div>
          <h2 
            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}
          >
            Balance Sheet
          </h2>
          <div 
            style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: "black" }}
          >
            {getDateDescription()}
          </div>
          <div 
            style={{ ...contentTextStyle, fontSize: "20px" }}
          >
            Acme Print
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse text-sm mt-4">
            <thead>
              {/* Month Header Row */}
              <tr className="text-black text-center bg-white">
                <th
                  className="text-left bg-white border-0"
                  style={{ ...contentTextStyle, color: 'black' }}
                ></th>
                <th
                  className="text-left bg-white border-0 pl-10"
                  style={{ ...contentTextStyle, color: 'black' }}
                  colSpan="1"
                >
                  {dateInfo.current}
                </th>
                <th
                  className="text-left bg-white border-0 pl-10"
                  style={{ ...contentTextStyle, color: 'black' }}
                  colSpan="2"
                >
                  {dateInfo.previousYear}
                </th>
                <th
                  className="text-left bg-white border-0 pl-10"
                  style={{ ...contentTextStyle, color: 'black' }}
                  colSpan="2"
                >
                  {dateInfo.previousMonth}
                </th>
              </tr>
              {/* Column Header Row */}
              <tr>
                <th
                  className="text-right text-white p-2 font-bold text-sm"
                  style={{
                    ...contentTextStyle,
                    color: 'white'
                  }}
                ></th>
                {/* Current Period */}
                <th
                  className="text-center text-white p-2 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    color: 'white',
                    borderRight: '20px solid white'
                  }}
                >
                  Actuals
                </th>

                {/* Prior Year Group */}
                <th
                  className="text-center text-white p-1 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    color: 'white'
                  }}
                >
                  Prior Year
                </th>
                <th
                  className="text-center text-white p-1 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    color: 'white',
                    borderRight: '20px solid white'
                  }}
                >
                  <div>Variance</div>
                </th>

                {/* Prior Month Group */}
                <th
                  className="text-center text-white p-2 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    color: 'white'
                  }}
                >
                  Prior Month
                </th>
                <th
                  className="text-center text-white p-2 font-bold text-sm"
                  style={{
                    backgroundColor: headerBgColor,
                    ...contentTextStyle,
                    color: 'white'
                  }}
                >
                  Variance
                </th>
              </tr>
            </thead>
            <tbody>
              {tableData.map((item, index) => renderTableRow(item, index))}
            </tbody>
          </table>
        </div>
        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>
          <p>
            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice
            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any
            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or
            delivered information.
          </p>
        </div>
      </div>
    </div>
  );
};
            
export default BalanceSheetDashboard;