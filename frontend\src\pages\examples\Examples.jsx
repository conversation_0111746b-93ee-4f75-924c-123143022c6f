import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { FaArrowDownLong } from "react-icons/fa6";
import { downloadMultipleFiles, getAllTemplates } from "../../services/example";
import Swal from "sweetalert2";
import * as XLSX from "xlsx";
import * as pdfjs from "pdfjs-dist/webpack";
import zipImg from "../../assets/example/Zip.png";

pdfjs.GlobalWorkerOptions.workerSrc = "/pdf.worker.min.js";

const Examples = () => {
  const [templates, setTemplates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [excelData, setExcelData] = useState({});
  const [downloading, setDownloading] = useState({});
  const [pdfLoadingStates, setPdfLoadingStates] = useState({});

  const handleDownload = async (template) => {
    try {
      setDownloading((prev) => ({ ...prev, [template.id]: true }));
      if (template.documents.length > 1) {
        await downloadZipFile(template);
      } else {
        const document = template.documents[0];
        const { file_key, file_name, file_type } = document;

        if (file_type === "TEMPLATE") {
          if (file_name.endsWith(".pdf")) {
            // For PDF, use the existing logic
            window.open(file_key, "_blank");
          } else if (file_name.endsWith(".xlsx")) {
            // For XLSX, simulate the download the same way as PDF
            await downloadFile(file_key, file_name);
          } else {
            console.error("Unsupported file type:", file_type);
          }
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setDownloading((prev) => ({ ...prev, [template.id]: false }));
    }
  };

  const downloadFile = (fileKey, fileName) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const link = document.createElement("a");
        link.href = fileKey;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        link.remove();
        resolve();
      }, 1000); // Simulate delay
    });
  };

  const loadExcelData = async (fileKey, templateId) => {
    try {
      const response = await fetch(fileKey);
      const blob = await response.blob();
      const reader = new FileReader();

      reader.onload = (e) => {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(sheet);
        setExcelData((prev) => ({ ...prev, [templateId]: jsonData }));
      };

      reader.readAsArrayBuffer(blob);
    } catch (error) {
      console.error("Error loading Excel file:", error);
    }
  };

  const downloadZipFile = async (template) => {
    const response = await downloadMultipleFiles(template.id);
    if (response.data) {
      const blob = new Blob([response.data], { type: "application/zip" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `${template.name}.zip`;
      link.click();
      URL.revokeObjectURL(link.href);
    } else {
      Swal.fire({
        icon: "error",
        title: "Error!",
        text: "Failed to download template file",
      });
    }
  };

  useEffect(() => {
    const getTemplates = async () => {
      try {
        setIsLoading(true);
        const response = await getAllTemplates();
        if (response.data) {
          const templates = response.data.body;
          setTemplates(templates);

          const initialLoadingStates = {};
          templates.forEach((template) => {
            if (
              template.documents.length === 1 &&
              template.documents[0].file_name.endsWith(".pdf")
            ) {
              initialLoadingStates[template.id] = true;
            }
          });
          setPdfLoadingStates(initialLoadingStates);

          // Load Excel data
          templates.forEach((template) => {
            if (
              template.documents.length === 1 &&
              template.documents[0].file_name.endsWith(".xlsx")
            ) {
              loadExcelData(template.documents[0].file_key, template.id);
            }
          });
        }
        setIsLoading(false);
      } catch (error) {
        console.log(error);
        setIsLoading(false);
      }
    };

    getTemplates();
  }, []);

  // Function to render the PDF page asynchronously
  const [pdfImages, setPdfImages] = useState({});

  // Modified PDF rendering function
  const renderPdfToImage = async (fileUrl, templateId) => {
    try {
      setPdfLoadingStates((prev) => ({ ...prev, [templateId]: true }));

      const pdf = await pdfjs.getDocument(fileUrl).promise;
      const page = await pdf.getPage(1);

      // Create temporary canvas
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      const viewport = page.getViewport({ scale: 1 });
      const scale = 345 / viewport.width;
      const scaledViewport = page.getViewport({ scale });

      canvas.width = scaledViewport.width;
      canvas.height = scaledViewport.height;

      await page.render({
        canvasContext: context,
        viewport: scaledViewport,
      }).promise;

      const imageUrl = canvas.toDataURL("image/png");
      setPdfImages((prev) => ({ ...prev, [templateId]: imageUrl }));
    } catch (error) {
      console.error("Error generating PDF image:", error);
    } finally {
      setPdfLoadingStates((prev) => ({ ...prev, [templateId]: false }));
    }
  };

  useEffect(() => {
    templates.forEach((template) => {
      if (
        template.documents.length === 1 &&
        template.documents[0].file_name.endsWith(".pdf")
      ) {
        renderPdfToImage(template.documents[0].file_key, template.id);
      }
    });
  }, [templates]);

  return (
    <div style={{ padding: "20px 50px", minHeight: "100vh" }}>
      <h1 className="px-4 mt-5 mb-7 text-2xl font-bold">Examples</h1>
      {isLoading ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "60vh",
          }}
        >
          <CircularProgress size={60} />
        </div>
      ) : templates.length === 0 ? (
        <div className="flex justify-center items-center h-60">
          <Typography variant="h5" className="text-gray-500">
            No Data Available
          </Typography>
        </div>
      ) : (
        <Grid container spacing={2}>
          {templates.map((card, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
              <Card
                sx={{
                  maxWidth: 345,
                  border: "1px solid #867f7f",
                  boxShadow: "none",
                  marginBottom: "15px",
                }}
              >
                <div className="flex justify-between items-center mx-5">
                  <CardContent>
                    <Typography
                      gutterBottom
                      variant="h5"
                      component="div"
                      className="!text-[16px] !font-semibold text-[#101828] !font-[inter]"
                    >
                      {card.name}
                    </Typography>
                    <Typography
                      className="!font-normal !text-[14px] text-[#475467] !font-[inter]"
                      variant="body2"
                    >
                      {card.description}
                    </Typography>
                  </CardContent>

                  <div className="flex gap-4 items-center">
                    <div
                      className="cursor-pointer hover:bg-[#e1e2e4] hover:rounded-xs"
                      onClick={() => handleDownload(card)}
                      style={{
                        pointerEvents: downloading[card.id] ? "none" : "auto",
                      }}
                    >
                      {downloading[card.id] ? (
                        <CircularProgress
                          size={24}
                          style={{ color: "#6A7A86" }}
                        />
                      ) : (
                        <FaArrowDownLong color="#6A7A86" />
                      )}
                    </div>

                    <div className="cursor-pointer">
                      <MoreVertIcon sx={{ color: "#6A7A86" }} />
                    </div>
                  </div>
                </div>

                {/* Preview for PDF files */}

                {card.documents.length === 1 &&
                  card.documents[0].file_type === "TEMPLATE" &&
                  card.documents[0].file_name.endsWith(".pdf") && (
                    <div className="w-full h-[200px] relative flex items-center justify-center">
                      {pdfLoadingStates[card.id] ? (
                        <CircularProgress />
                      ) : (
                        pdfImages[card.id] && (
                          <img
                            src={pdfImages[card.id]}
                            width="100%"
                            alt="PDF preview"
                            className="w-full h-full object-cover
                            "
                            style={{ boxShadow: "0 2px 4px rgba(0,0,0,0.1)" }}
                          />
                        )
                      )}
                    </div>
                  )}

                {/* Preview for XLS files */}
                {card.documents.length === 1 &&
                  card.documents[0].file_name.endsWith(".xlsx") && (
                    <div style={{ width: "100%", height: 200 }}>
                      {excelData[card.id] ? (
                        <table
                          style={{
                            width: "100%",
                            borderCollapse: "collapse",
                            tableLayout: "fixed",
                          }}
                        >
                          <thead>
                            <tr>
                              {Object.keys(excelData[card.id][0]).map(
                                (header, idx) => (
                                  <th
                                    key={idx}
                                    style={{
                                      border: "1px solid #ddd",
                                      padding: "5px",
                                      textOverflow: "ellipsis",
                                      whiteSpace: "nowrap",
                                      overflow: "hidden",
                                    }}
                                  >
                                    {header}
                                  </th>
                                )
                              )}
                            </tr>
                          </thead>
                          <tbody>
                            {excelData[card.id]?.slice(0, 5).map((row, idx) => (
                              <tr key={idx}>
                                {Object.values(row).map((cell, idx) => (
                                  <td
                                    key={idx}
                                    style={{
                                      border: "1px solid #ddd",
                                      padding: "5px",
                                      textOverflow: "ellipsis",
                                      whiteSpace: "nowrap",
                                      overflow: "hidden",
                                    }}
                                  >
                                    {cell}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      ) : (
                        <div className="flex items-center justify-center">
                          <CircularProgress />
                        </div>
                      )}
                    </div>
                  )}

                {/* Preview for multiple files (ZIP) */}
                {card.documents.length > 1 && (
                  <div
                    style={{
                      width: "100%",
                      height: 200,
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <img src={zipImg} width={200} height={200} />
                  </div>
                )}
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </div>
  );
};

export default Examples;
