import { prisma } from "../db/prisma.js";
import { uploadFileOnS3, generateSignedUrl, deleteFileFromS3 } from "../utils/aws-s3.js";
import path from 'path'
import fs from 'fs';
import AWS from 'aws-sdk';
import pkg from '@prisma/client';
const { FileType } = pkg;
import JSZip from 'jszip';

const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,  // Make sure to use environment variables for credentials
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,  // specify your region
});


const __dirname = path.resolve();

const uploadMultipleFilesOnS3 = async (files) => {
    let results = [];
    results = await Promise.all(files.map(async (file) => {
        const fileNameWithoutExtension = path.parse(file.originalname).name;
        const uniqueFileName = `${Date.now()}-${fileNameWithoutExtension}`;
        const fileBuffer = file.buffer;
        const sizeInBytes = fileBuffer.length;
        const base64File = fileBuffer.toString('base64');
        const fileName = `/uploads/reports/${uniqueFileName}`;
        const file_uploaded = await uploadFileOnS3(base64File, fileName);

        return {
            originalName: file.originalname,
            s3Key: file_uploaded.Key,
            location: file_uploaded.Location,
            size: sizeInBytes,
        };
    }));

    return results
}
export const createExampleTemplate = async (req) => {
    let uploadFiles = [];
    try {
        const { name, description } = req.body;
        uploadFiles = await Promise.all(req.files.map(async (file) => {
            const fileNameWithoutExtension = path.parse(file.originalname).name;
            const uniqueFileName = `${Date.now()}-${fileNameWithoutExtension}`;
            const fileBuffer = file.buffer;
            const sizeInBytes = fileBuffer.length;
            const base64File = fileBuffer.toString('base64');
            const fileName = `/uploads/reports/${uniqueFileName}`;
            const file_uploaded = await uploadFileOnS3(base64File, fileName);

            return {
                originalName: file.originalname,
                s3Key: file_uploaded.Key,
                location: file_uploaded.Location,
                size: sizeInBytes,
            };
        }));

        if (uploadFiles.length === 0) {
            return {
                success: false,
                statusCode: 400,
                message: 'No file uploaded',
            }
        }

        const documentsPayload = uploadFiles.map((file) => ({
            file_type: FileType.TEMPLATE,
            file_name: file.originalName,
            file_key: file.s3Key,
            size: JSON.stringify(file.size),
        }));

        const result = await prisma.$transaction(async (prisma) => {
            const result = await prisma.templates.create({
                data: {
                    name,
                    description,
                    documents: {
                        create: documentsPayload,
                    },
                },
                include: {
                    documents: true,
                },
            });

            return result;
        });
        return {
            success: true,
            statusCode:201,
            message: 'Example created successfully',
            body: result
        };
    } catch (error) {
        console.log("error in creating template ----", error)

        if (uploadFiles && uploadFiles.length > 0) {
            await Promise.all(
                uploadFiles.map(async (file) => {
                    try {
                        await deleteFileFromS3(file.s3Key); // Add a function to delete files from S3
                    } catch (deleteError) {
                        console.error(`Failed to delete file ${file.s3Key} from S3`, deleteError);
                    }
                })
            );
        }
        throw {
            status: error.status,
            success: false,
            statusCode: error.statusCode,
            message: error.message || 'Internal Server Error',
        };
    }
};

export const getAllTemplates = async () => {
    try {
        const templates = await prisma.templates.findMany({
            include: {
                documents: true,
            },
            orderBy: {
                createdAt: 'desc',
            }
        });

        for (const template of templates) {
            for (const document of template.documents) {
                document.file_key = await generateSignedUrl(document.file_key);
            }
        }

        return {
            success: true,
            statusCode:200,
            message: 'Templates fetched successfully',
            body: templates,
        };
    } catch (error) {
        throw {
            status: error.status,
            success: false,
            statusCode: error.statusCode,
            message: error.message || 'Internal Server Error',
        };
    }
};

const getTemplate = async (id) => {
    const template = await prisma.templates.findUnique({
        where: {
            id: parseInt(id),
        },
        include: {
            documents: true,
        },
    });

    if (!template) {
        throw {
            status: 404,
            success: false,
            statusCode: 404,
            message: 'Template not found',
        };
    }

    return template;
}

const zipFiles = async (files, zipPath) => {
    const zip = new JSZip();

    for (const file of files) {
        const fileName = path.basename(file);
        zip.file(fileName, fs.readFileSync(file));
    }

    const zipContent = await zip.generateAsync({ type: "nodebuffer" });
    fs.writeFileSync(zipPath, zipContent);

    return zipPath;
};

const downloadFilesFromS3 = async (documents, outputDir) => {
    const downloadedFiles = [];

    for (const document of documents) {
        const filePath = path.join(outputDir, document.file_name);
        const fileStream = fs.createWriteStream(filePath);

        const params = {
            Bucket: process.env.S3_BUCKET_NAME,
            Key: document.file_key,
        };

        await new Promise((resolve, reject) => {
            s3.getObject(params)
                .createReadStream()
                .pipe(fileStream)
                .on("finish", () => {
                    downloadedFiles.push(filePath);
                    resolve();
                })
                .on("error", reject);
        });
    }

    return downloadedFiles;
};

// Cleanup function to delete files and ZIP
const cleanup = async (files, zipFilePath) => {
    try {
        // Delete each downloaded file
        for (const file of files) {
            if (fs.existsSync(file)) {
                await fs.promises.unlink(file);
                console.log(`Deleted file: ${file}`);
            }
        }

        // Delete the ZIP file
        if (fs.existsSync(zipFilePath)) {
            await fs.promises.unlink(zipFilePath);
            console.log(`Deleted ZIP file: ${zipFilePath}`);
        }
    } catch (error) {
        console.error("Error during cleanup:", error);
    }
};

export const downloadTemplateFiles = async (req, res) => {
    let downloadedFiles = [];
    let zipFilePath = null;

    try {
        const { id } = req.params;
        const template = await getTemplate(parseInt(id));

        if (!template.documents?.length) {
            return res.status(200).json({
                success: false,
                statusCode:404,
                message: 'No documents found for this template.'
            });
        }

        const outputDir = path.join(__dirname, "downloads");
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir);
        }

        const zipPath = path.join(outputDir, `${template.name}-${Date.now()}.zip`);
        downloadedFiles = await downloadFilesFromS3(template.documents, outputDir);
        zipFilePath = await zipFiles(downloadedFiles, zipPath);

        res.download(zipFilePath, `${template.name}.zip`, (err) => {
            if (err) {
                console.error("Error sending download:", err);
                if (!res.headersSent) {
                    res.status(500).json({
                        success: false,
                        message: "Failed to download files.",
                    });
                }
            }

            cleanup(downloadedFiles, zipFilePath);
        });

    } catch (error) {
        console.error("Error in downloadTemplateFiles:", error);

        // Clean up any created files
        cleanup(downloadedFiles, zipFilePath);

        if (!res.headersSent) {
            res.status(500).json({
                success: false,
                message: error.message || "Internal Server Error",
            });
        }
    }
};

export const deleteTemplateByID = async (req) => {
    const { id } = req.params;
    return await prisma.$transaction(async (tx) => {
        try {
            // Step 1: Fetch the template and its associated documents
            const template = await tx.templates.findUnique({
                where: { id: Number(id) },
                include: { documents: true },
            });

            if (!template) {
                return {
                    success:true,
                    statusCode:404,
                    message:'Template not found'
                }
            }

            // Step 2: Delete associated documents from S3
            const deleteS3Promises = template.documents.map(async (document) => {
                await deleteFileFromS3(document.file_key);
            });

            // Step 3: Wait for all S3 deletions to finish
            await Promise.all(deleteS3Promises);

            // Step 4: Delete documents from the database (cascade delete will be handled by Prisma)
            await tx.document.deleteMany({
                where: {
                    templateId: Number(id),
                },
            });

            await tx.templates.delete({
                where: { id: Number(id) },
            });

            return { success: true, message: 'Template and associated documents deleted successfully' };

        } catch (err) {
            console.log("error in deleting template ----", err);
            throw err;
        }
    })
}

export const updateTemplateByID = async (req) => {
    const { templateId } = req.params;
    try {
        const template = await prisma.templates.findUnique({
            where: { id: Number(templateId) },
            include: { documents: true },
        });

        if (!template) {
            return {
                success:false,
                statusCode:404,
                message:"Template not found"
            }
        }

        for (const doc of template.documents) {
            if (doc.file_key) {
                await deleteFileFromS3(doc.file_key);
            }
        }

        const uploadedFiles = await uploadMultipleFilesOnS3(req.files);
        const documentsPayload = uploadedFiles.map((file) => ({
            templateId: Number(templateId),
            file_type: FileType.TEMPLATE,
            file_name: file.originalName,
            file_key: file.s3Key,
            size: JSON.stringify(file.size),
        }));

        return await prisma.$transaction(async (tx) => {
            await tx.templates.update({
                where: { id: Number(templateId) },
                data: {
                    name: req.body?.name || template.name,
                    description: req.body?.description || template.description,
                },
            });

            //Delete all associated documents from the database
            await tx.document.deleteMany({
                where: { templateId: Number(templateId) },
            });

            await tx.document.createMany({
                data: documentsPayload
            });

            return {
                success: true,
                message: 'Template and documents updated successfully',
                template: await prisma.templates.findUnique({
                    where: { id: Number(templateId) },
                    include: { documents: true },
                })
            };
        });

    } catch (err) {
        console.log("error in updating template ----", err);
        throw err;
    }
}