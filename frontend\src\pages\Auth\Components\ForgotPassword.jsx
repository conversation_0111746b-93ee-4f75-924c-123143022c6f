import React from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import Cookies from "js-cookie";
import { useNavigate } from "react-router-dom";
import logo from "../../../assets/forgotpassword.svg";
import Swal from "sweetalert2";
import { forgotPassword } from "../../../services/auth";

const ForgotPassword = () => {
  const navigate = useNavigate();

  const validationSchema = Yup.object({
    email: Yup.string()
      .email("Please enter a valid email address")
      .required("Email is required"),
  });

  const handleSubmit = async (values, { setSubmitting, setErrors }) => {
    try {
      const { email } = values;

      const response = await forgotPassword(email);
      if (response?.statusCode === 200 || response?.data?.statusCode === 200) {
      Cookies.set("email", email, { expires: 30 });

      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "success",
        title: "Reset link sent successfully!",
        showConfirmButton: false,
        timer: 1000,
        timerProgressBar: true,
      });

      setTimeout(() => {
        navigate("/check-email");
      }, 1000); 
    }
    else {
      const errorMessage = response?.data?.message || "Failed to send reset email. Please try again.";
      throw new Error(errorMessage);
    }

    } catch (error) {
      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: "Failed to send reset email. Please try again.",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });

      setErrors({ api: error.message });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center h-screen bg-gray-50">
      <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-lg">
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <img src={logo} alt="Logo" className="w-12 h-12" />
        </div>

        {/* Title */}
        <h2 className="text-2xl font-semibold text-center mb-2">
          Forgot password?
        </h2>
        <p className="text-sm text-gray-500 text-center mb-6">
          No worries, we’ll send you reset instructions.
        </p>

        {/* Formik Form */}
        <Formik
          initialValues={{ email: "" }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, errors }) => (
            <Form className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <Field
                  type="email"
                  name="email"
                  placeholder="Enter your email"
                  className="mt-1 w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <ErrorMessage
                  name="email"
                  component="div"
                  className="text-sm text-red-500 mt-1"
                />
              </div>

              {errors.api && (
                <div className="text-sm text-red-500 text-center">
                  {errors.api}
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {isSubmitting ? "Sending..." : "Reset password"}
              </button>
            </Form>
          )}
        </Formik>

        {/* Back to Login */}
        <div className="mt-4 text-center">
          <button
            type="button"
            onClick={() => navigate("/login")}
            className="text-sm text-blue-500 hover:underline"
          >
            ← Back to log in
          </button>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
