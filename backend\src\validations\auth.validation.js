import Joi from 'joi';

export const authValidation = {
  register: Joi.object().keys({
    username: Joi.string().required().messages({
      'any.required': 'username is required',
    }),
    email: Joi.string().required().messages({
      'any.required': 'email is required',
    }),
    password: Joi.string().required().messages({
      'any.required': 'password is required',
    }),
    isAdmin:Joi.boolean().required().messages({
      'any.required':"isAdmin check is required"
    })
  }),
  login: Joi.object().keys({
    email: Joi.string().required().messages({
      'any.required': 'email is required',
    }),
    password: Joi.string().required().messages({
      'any.required': 'password is required',
    }),
  }),
  forgetPassword: Joi.object().keys({
    email: Joi.string().required().messages({
      'any.required': 'email is required',
    }),
  }),
  resetPassword: Joi.object().keys({
    password: Joi.string().required().messages({
      'any.required': 'password is required',
    }),
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.required': 'Confirm password is required',
        'any.only': 'Passwords do not match',
      }),
    token: Joi.string().required().messages({
      'any.required': 'token is required',
    }),
  }),
};
