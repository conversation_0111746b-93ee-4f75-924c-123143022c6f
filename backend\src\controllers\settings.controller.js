
import { HttpStatusCode } from '../enums/error.enum.js';
import { defaultUsersList, getSharedUsersList, revokeAccessForSharedUser, addUserToShareCompanyWith } from '../services/settings.service.js';
import { createErrorResponse } from '../utils/response.js';


export const getDefaultUsersList = async (req, res) => {
    try {
        const { page = 1, pageSize = 10, search = '', users = [], isUserAssociation = false ,companyId} = req.body;
        const pageNum = Number(page) || 1;
        const pageSizeNum = Number(pageSize) || 10;
        const userIds = Array.isArray(users) ? users : [];
        const ownerCompanyId = Number(companyId??"")        

        const { id: userId } = req.user;
        res.json(await defaultUsersList(userId, pageNum, pageSizeNum, search, isUserAssociation, userIds,ownerCompanyId));
    } catch (error) {
        console.log("error", error)
        throw createErrorResponse(err.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR, 'Failed to fetch default users list')
    }
}

export const AddShareWithUser = async (req, res) => {
    try {
        res.json(await addUserToShareCompanyWith(req.user.id, req.body));
    } catch (error) {
        console.log("error", error)
        throw createErrorResponse(error.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR, 'Failed to fetch default users list')
    }
}

export const getSharedUsers = async (req, res) => {
    try {
        res.json(await getSharedUsersList(req.user.id));
    } catch (error) {
        console.log("error", error)
        throw createErrorResponse(error.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR, 'Failed to fetch shared users list')
    }
}

export const deleteSharedUser = async (req, res) => {
    try {
        res.json(await revokeAccessForSharedUser(Number(req.user.id), Number(req.params.id)));
    } catch (error) {
        console.log("error", error)
        throw createErrorResponse(error.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR, 'Failed to delete shared user')
    }
}
