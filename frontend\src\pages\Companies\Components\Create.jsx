import * as React from "react";
import Button from "@mui/material/Button";
import Swal from "sweetalert2";
import { Box, Grid, Chip } from "@mui/material";
import { styled } from "@mui/material/styles";
import ButtonGroup from "@mui/material/ButtonGroup";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import { Formik, Field, Form } from "formik";
import { Country, State, City } from "country-state-city";
import { create } from "../../../services/company";
import AddPhotoAlternateOutlinedIcon from "@mui/icons-material/AddPhotoAlternateOutlined";
import * as Yup from "yup";
import { AvailableMarkets } from "../../../services/markets";
import { CircularProgress } from "@mui/material";
import PersonAddAltOutlinedIcon from "@mui/icons-material/PersonAddAltOutlined";
import SharedUserSettingsModal from "./ShareUsersModal";
import {
  getSharedUsersList,
  getDefaultUsersList,
  revokeAccess,
} from "../../../services/settings";
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

export default function CreateCompany({ onClose }) {
  const [data, setData] = React.useState({});
  const [open, setOpen] = React.useState(false);
  const [isDragging, setIsDragging] = React.useState(false);
  const [location, setLocation] = React.useState({
    country: "",
    state: "",
    city: "",
  });
  const [countries, setCountries] = React.useState([]);
  const [states, setStates] = React.useState([]);

  const [cities, setCities] = React.useState([]);
  const [imageFormat, setImageFormat] = React.useState(false);
  const [logoPreview, setLogoPreview] = React.useState(null);
  const [loading, setLoading] = React.useState(false);
  const [openModal, setOpenModal] = React.useState(false);
  const [addedUsers, setAddedUsers] = React.useState([]);
  const [isCreatedModal, setIsCreatedModal] = React.useState(false);
  const handleOpenModal = () => {
    getUsersList(1, 10, '', addedUsers.map((user) => user?.userId ? user?.userId : user?.id));
    setOpenModal(true);
  }

  const handleCloseModal = () => {
    setOpenModal(false)
  };

  const handleDelete = (deletedUser) => {
    setAddedUsers((prevUsers) => prevUsers.filter((user) => user.id !== deletedUser.id));
  };

  const handleAddUser = (newUser) => {
    setAddedUsers((prevUsers) => {
        if (prevUsers?.some((u) => u.id === newUser.id)) return prevUsers; // Avoid duplicates
        return [...prevUsers, newUser]; // Append new user
    });
};


  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);

    setLogoPreview(null);
    sharedUsersList();
    onClose();
  };

  const handleFileChange = (event, setFieldValue) => {
    const file = event.target.files[0];
    if (file) {
      const maxFileSize = 2 * 1024 * 1024;
      const allowedFileTypes = [
        "image/svg+xml",
        "image/png",
        "image/jpeg",
        "image/gif",
      ];

      if (!allowedFileTypes.includes(file.type)) {
        setImageFormat(true);
        return;
      } else {
        setImageFormat(false);
      }

      if (file.size > maxFileSize) {
        return;
      }

      const img = new Image();
      img.src = URL.createObjectURL(file);
      img.onload = () => {
        setLogoPreview(URL.createObjectURL(file));
        const reader = new FileReader();
        reader.onload = () => {
          const base64String = reader.result.split(",")[1];
          setFieldValue("logo", base64String);
        };
        reader.onerror = (error) => {
          console.error("Error reading file:", error);
        };
        reader.readAsDataURL(file);
      };

      img.onerror = () => {
        console.error("Error loading image for dimension validation.");
      };

      img.onloadend = () => {
        URL.revokeObjectURL(img.src);
      };
    }
  };

  const initialValues = {
    name: "",
    fiscal_year_end: "",
    country: "",
    state: "",
    city: "",
    description: "",
    naics: "",
    logo: "",
    market: "",
    users: [],
  };

  const sharedUsersList = async () => {
    const sharedUsers = await getSharedUsersList();

    if (sharedUsers?.data?.statusCode === 200) {
      setAddedUsers(
        sharedUsers?.data?.users?.map((item) => ({
          username: item?.sharedWithUser?.username,
          userId: item?.sharedWithUser?.id,
          id: item?.id,
        })) || []
      );
    }
  };

  React.useEffect(() => {
    const allCountries = Country.getAllCountries();
    const usIndex = allCountries.findIndex(
      (country) => country.name === "United States"
    );
    const australiaIndex = allCountries.findIndex(
      (country) => country.name === "Australia"
    );

    if (usIndex !== -1 && australiaIndex !== -1) {
      const [usCountry] = allCountries.splice(usIndex, 1);
      const [australiaCountry] = allCountries.splice(
        australiaIndex - (usIndex < australiaIndex ? 1 : 0),
        1
      );

      allCountries.unshift(usCountry);
      allCountries.splice(1, 0, australiaCountry);
    }

    setCountries(allCountries);
    sharedUsersList();
  }, []);

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .required("Company name is required")
      .min(2, "Company name must be at least 2 characters"),
    fiscal_year_end: Yup.date().required("Fiscal year end is required"),
    description: Yup.string()
      .required("Critical company info is required")
      .min(10, "Critical company info must be at least 10 characters"),
    naics: Yup.string().required("NAICS code is required"),
  });

  const handleSubmit = async (values) => {
    setLoading(true);
    const payload = {
      name: values.name,
      fiscal_year_end: values.fiscal_year_end,
      country: countries.find((country) => country.isoCode === location.country)
        ?.name,
      state: location.state
        ? states.find((state) => state.isoCode === location.state)?.name
        : "",
      city: location.city || "",
      description: values.description,
      naics: values.naics,
      logo: values.logo,
      market: values.market,
      users: addedUsers?.map((user) => user?.userId ? user?.userId : user?.id) || []
    };
    try {
      const company = await create(payload);
      if (company.status === 200) {
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Company created",
        });
        handleClose();
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      Swal.fire({
        toast: true,
        position: "top-end",
        icon: "error",
        title: `${error.message} Please try again.`,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDrop = (event, setFieldValue) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    handleFileChange({ target: { files: [file] } }, setFieldValue);
    setIsDragging(false);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleCountryChange = (e) => {
    const selectedCountryIsoCode = e.target.value;
    setLocation({
      ...location,
      country: selectedCountryIsoCode,
      state: "",
      city: "",
    });
    const allStates = State.getStatesOfCountry(selectedCountryIsoCode);
    setStates(allStates);
  };

  const handleStateChange = (e) => {
    const selectedStateIsoCode = e.target.value;
    setLocation({ ...location, state: selectedStateIsoCode, city: "" });

    const allCities = City.getCitiesOfState(
      location.country,
      selectedStateIsoCode
    );    
    const filteredCities = allCities.filter(
      city => !city.name.toLowerCase().includes("county")
    );
    setCities(filteredCities);
  };


  const handleCityChange = (e) => {
    setLocation({ ...location, city: e.target.value });
  };

  const getUsersList = async (page = 1, pageSize = 10, search = '', addedUsersList = []) => {
    const payload = { page, pageSize, search, users: addedUsersList, isUserAssociation: true };
    const users = await getDefaultUsersList(payload);
    if (users?.data?.statusCode === 200) {
      setData(users?.data)
    }
  }

  return (
    <React.Fragment>
      <Button variant="contained" size="medium" onClick={handleClickOpen}>
        Create New
      </Button>
      <BootstrapDialog
        fullWidth
        maxWidth="md"
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={open}
      >
        <DialogTitle
          sx={{ m: 0, p: 2 }}
          id="customized-dialog-title"
          className="font-bold"
        >
          Create New Company
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Fill up the following fields to create a new company profile.
          </Typography>
        </DialogTitle>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={(theme) => ({
            position: "absolute",
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
          })}
        >
          <CloseIcon />
        </IconButton>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={validationSchema}
        >
          {({ setFieldValue, values, errors, touched, isSubmitting }) => (
            <Form>
              <DialogContent dividers>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Company Name
                    {touched.name && Boolean(errors.name) ? (
                    <span className="text-red-500">*</span>
                  ) : (
                    "*"
                  )}
                  </Typography>
                  <Field
                    as={TextField}
                    fullWidth
                    margin="dense"
                    label="e.g. Linear"
                    name="name"
                    variant="outlined"
                    error={touched.name && Boolean(errors.name)}
                    helperText={touched.name && errors.name}
                  />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Fiscal Year End
                    {touched.fiscal_year_end &&
                  Boolean(errors.fiscal_year_end) ? (
                    <span className="text-red-500">*</span>
                  ) : (
                    "*"
                  )}
                  </Typography>
                  <Field
                    as={TextField}
                    fullWidth
                    margin="normal"
                    label="select date"
                    name="fiscal_year_end"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                    error={
                      touched.fiscal_year_end && Boolean(errors.fiscal_year_end)
                    }
                    helperText={
                      touched.fiscal_year_end && errors.fiscal_year_end
                    }
                  />
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Location
                  </Typography>
                  <div
                    style={{
                      display: "flex",
                      gap: "20px",
                      marginTop: "10px",
                      flexGrow: 1,
                    }}
                  >
                    <ButtonGroup fullWidth aria-label="Basic button group">
                      <Select
                        fullWidth
                        onChange={handleCountryChange}
                        value={location.country}
                        displayEmpty
                        error={touched.country && Boolean(errors.country)}
                        helperText={touched.country && errors.country}
                      >
                        <MenuItem value="" disabled>
                          Select Country
                        </MenuItem>
                        {countries.map((country) => (
                          <MenuItem key={country.id} value={country.isoCode}>
                            {country.name}
                          </MenuItem>
                        ))}
                      </Select>

                      <Select
                        fullWidth
                        value={location.state}
                        onChange={handleStateChange}
                        displayEmpty
                        disabled={!location.country}
                      >
                        <MenuItem value="" disabled>
                          Select State
                        </MenuItem>
                        {states.map((state) => (
                          <MenuItem key={state.isoCode} value={state.isoCode}>
                            {state.name}
                          </MenuItem>
                        ))}
                      </Select>
                      <Select
                        fullWidth
                        value={location.city}
                        onChange={handleCityChange}
                        displayEmpty
                        disabled={!location.state}
                      >
                        <MenuItem value="" disabled>
                          Select City
                        </MenuItem>
                        {cities.map((city) => (
                          <MenuItem key={city.id} value={city.name}>
                            {city.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </ButtonGroup>
                  </div>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Market
                  </Typography>
                  <div
                    style={{
                      display: "flex",
                      gap: "20px",
                      marginTop: "20px",
                      flexGrow: 1,
                    }}
                  >
                    <Select
                      fullWidth
                      displayEmpty
                      name="market"
                      value={values.market}
                      onChange={(e) => setFieldValue("market", e.target.value)}
                    >
                      <MenuItem value="" disabled>
                        Select Market
                      </MenuItem>
                      {AvailableMarkets.map((market) => (
                        <MenuItem key={market} value={market}>
                          {market}
                        </MenuItem>
                      ))}
                    </Select>
                  </div>
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Naics
                    {touched.naics && Boolean(errors.naics) ? (
                    <span className="text-red-600">*</span>
                  ) : (
                    "*"
                  )}
                  </Typography>
                  <Field
                    as={TextField}
                    fullWidth
                    margin="normal"
                    label="Naics"
                    name="naics"
                    placeholder="Type here"
                    error={touched.naics && Boolean(errors.naics)}
                    helperText={touched.naics && errors.naics}
                  />
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Critical Company Info
                    {touched.description && Boolean(errors.description) ? (
                    <span className="text-red-600">*</span>
                  ) : (
                    "*"
                  )}
                  </Typography>
                  <Field
                    as={TextField}
                    fullWidth
                    margin="normal"
                    label="Critical company info"
                    name="description"
                    placeholder="Type here"
                    multiline
                    rows={3}
                    error={touched.description && Boolean(errors.description)}
                    helperText={touched.description && errors.description}
                  />
                </Box>

                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                  {/* Company Logo */}
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Company logo
                  </Typography>

                  {!logoPreview ? (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        background: "#EFF3F6",
                        padding: "12px 14px",
                        borderRadius: "50%",
                        width: "64px",
                        height: "64px",
                        overflow: "hidden",
                      }}
                    >
                      <AddPhotoAlternateOutlinedIcon fontSize="large" />
                    </Box>
                  ) : (
                    <img
                      src={logoPreview}
                      alt="Uploaded logo"
                      style={{
                        width: "64px",
                        height: "64px",
                        objectFit: "cover",
                        borderRadius: "50%",
                      }}
                    />
                  )}

                  {/* Upload Button */}
                  <Box
                    component="label"
                    onDrop={(event) => handleDrop(event, setFieldValue)}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    sx={{
                      flexGrow: 1,
                      marginTop: "8px",
                      marginLeft: "2px",
                      padding: "10px 20px",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      textAlign: "center",
                      textTransform: "none",
                      border: isDragging
                        ? "2px solid grey"
                        : "1px solid #e0e0e0",
                      borderRadius: "4px",
                      backgroundColor: isDragging ? "#f5f5f5" : "transparent",
                      borderColor: isDragging ? "#0078d4" : "#e0e0e0",
                      cursor: "pointer",
                      "&:hover": {
                        borderColor: "#0078d4",
                      },
                    }}
                  >
                    <Typography variant="body1" sx={{ whiteSpace: "nowrap" }}>
                      <span style={{ color: "#033BD7", fontWeight: 600 }}>
                        Click to upload
                      </span>{" "}
                      or drag and drop
                    </Typography>
                    <Typography
                      variant="caption"
                      sx={{
                        marginTop: "8px",
                        color: "#2E3A44",
                        fontWeight: "400",
                      }}
                    >
                      SVG, PNG, JPG, or GIF (max. 800x400px)
                    </Typography>
                    <input
                      hidden
                      accept="image/*"
                      multiple
                      type="file"
                      onChange={(event) =>
                        handleFileChange(event, setFieldValue)
                      }
                    />
                  </Box>
                </Box>

                <Typography>
                  {imageFormat && (
                    <div className="flex item-center justify-center text-red-600 font-normal ml-[16rem] mt-[6px]">
                      Format must be .png, .gif, .jpeg, .svg
                    </div>
                  )}
                </Typography>

                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                    width: "100%",
                    flex: 1,
                    marginTop: "20px",
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ minWidth: "200px", fontWeight: 500 }}
                  >
                    Share With
                  </Typography>

                  <Box
                    sx={{
                      flex: 1,
                      display: "flex",
                      alignItems: "center",
                      gap: "20px",
                      marginLeft: "-10px",
                    }}
                  >
                    <Button
                      onClick={handleOpenModal}
                      sx={{
                        ml: 1,
                        borderRadius: "50%",
                        background: "#EFF3F6",
                        padding: "30px",
                        width: "50px",
                        height: "50px",
                        position: "relative",
                      }}
                    >
                      <PersonAddAltOutlinedIcon
                        fontSize="large"
                        sx={{ color: "black" }}
                      />
                    </Button>

                    <Box
                      sx={{
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 1,
                        minHeight: "40px",
                        maxHeight: "140px",
                        overflowY: "auto",
                        p: 1,
                        border: "1px solid #ccc",
                        borderRadius: "5px",
                        width: "100%",
                      }}
                    >
                      {addedUsers?.map((user) => (
                        <Chip
                          key={user.id}
                          label={user.username}
                          onDelete={() => handleDelete(user)}
                          sx={{ m: 0.5 }}
                          variant="outlined"
                        />
                      ))}
                    </Box>

                    <SharedUserSettingsModal
                      open={openModal}
                      data={data}
                      onClose={handleCloseModal}
                      onAddUser={handleAddUser}
                      addedUsers={addedUsers}
                      isCreatedModal={isCreatedModal}
                      fetchUsers={getUsersList}
                    />
                  </Box>
                </Box>
              </DialogContent>
              <DialogActions>
                <Grid container spacing={2} className="p-3">
                  <Grid item xs={6}>
                    <Button onClick={handleClose} variant="outlined" fullWidth>
                      Cancel
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      fullWidth
                      disabled={isSubmitting}

                    >
                      {loading ? (
                        <CircularProgress size={20} sx={{ color: "white" }} />
                      ) : (
                        "Save"
                      )}
                    </Button>
                  </Grid>
                </Grid>
              </DialogActions>
            </Form>
          )}
        </Formik>
      </BootstrapDialog>
    </React.Fragment>
  );
}
