import nodemailer from 'nodemailer';
import sgMail from '@sendgrid/mail';

sgMail.setApiKey(process.env.SENDGRID_API_KEY)
// const sendGridTransport = require('nodemailer-sendgrid-transport');

// const transporter = nodemailer.createTransport(
//   sendGridTransport({
//     auth: {
//       api_key: process.env.CONFIG_SENDGRID_API_KEY,
//     },
//   }),
// );


export const sendEmail = async (receiverMail, subject, body, attachments = []) => {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.SUPPORT_EMAIL,
      pass: process.env.gmailPass,
    },
    tls: {
      rejectUnauthorized: false,
    },
  });
  // Define the email options
  const mailOptions = {
    from: process.env.SUPPORT_EMAIL,
    from_name: process.env.FROM_NAME,
    to: receiverMail,
    subject: subject,
    html: body,
    attachments
  };


  transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
      console.log('Mail Error :- ', { error });
    } else {
      return { status: true, message: info.messageId };
    }
  });
};

export const sendEmailWithTemplate = async (receiverMail, subject, body, attachments = []) => {
  try {
    const msg = {
      to: receiverMail,
      from: process.env.NOTIFICATION_EMAIL,
      from_name: process.env.FROM_NAME,
      subject,
      html: body,
      attachments
    };

    console.log("msg _______", msg)
    const response = await sgMail.send(msg);
    console.log("response", response)
  } catch (error) {
    console.error('Error sending template email:', error);

    if (error.response) {
      console.error('Error details:', error.response.body);
    }
  }
};

export const downloadReportEmailTemplate = (data) => {
  return `
    <html>
      <body>
        <p>Dear ${data.recipientName},</p>
        <p>This is to inform you that the <strong>${data.reportName}</strong> for <strong>${data.companyName}</strong> is now ready for download.</p>
        <p>You can access the report at <a href=${data.fileUrl}>${data.fileName}</a>. If you encounter any issues with the download or require additional assistance, please do not hesitate to contact us.</p>
        <p>Thank you</p>
        <p>${process.env.SUPPORT_EMAIL}</p>
      </body>
    </html>
  `;
}

export const editReportEmailTemplate = (data) => {
  return `
    <html>
      <body>
        <p>Dear ${data.recipientName},</p>
        <p>This is to inform you that the <strong>${data.reportName}</strong> for <strong>${data.companyName}</strong> is now ready for editing.</p>
        <p>Please access the report at <a href=${data.url}>${data.reportName}</a> and make any necessary edits. If you have any questions or require additional details, feel free to reach out</p>
        <p>Thank you</p>
        <p>${process.env.SUPPORT_EMAIL}</p>
      </body>
    </html>
  `;
}