import React from 'react';
import { reportSummaryData } from './data.js';

const ReportSummary = ({
  headerTextStyle = {},
  headingTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {}
}) => {

  // Helper function to render content items
  const renderContentItem = (item, index) => {
    if (typeof item === 'string') {
      return (
        <p key={index} className="leading-relaxed" style={contentTextStyle}>
          {item}
        </p>
      );
    }

    if (item.type === 'paragraph') {
      return (
        <p key={index} className="leading-relaxed" style={contentTextStyle}>
          <span className="font-semibold">{item.text.split(':')[0]}:</span> {item.text.split(':')[1]}
        </p>
      );
    }

    if (item.type === 'subsection') {
      return (
        <div key={index}>
          <p className="leading-relaxed font-semibold" style={{ ...subHeadingTextStyle, fontWeight: "bold", color: "black", marginTop: '-4px', marginBottom: '-4px' }}>
            {item.title}
          </p>
          <div>
            {item.items.map((subItem, subIndex) => (
              <p key={subIndex} className="leading-relaxed" style={contentTextStyle}>
                {subItem}
              </p>
            ))}
          </div>
        </div>
      );
    }

    return null;
  };

  // Helper function to render analysis sections
  const renderAnalysisSection = (section, index) => (
    <div key={index} className="mb-2">
      <h3
        style={{ ...headingTextStyle, color: 'black', marginBottom: "-7px" }}
      >
        {section.title}
      </h3>
      <div>
        {section.content.map((item, itemIndex) => renderContentItem(item, itemIndex))}
      </div>

      <div>
        <div
          style={{ ...contentTextStyle, fontSize: "20px", fontWeight: "bold", color: 'black', marginTop: '-5px', marginBottom: "-5px" }}
        >
          Recommendation
        </div>
        <div
          className="text-gray-700 leading-relaxed"
          style={contentTextStyle}
        >
          {section.recommendation}
        </div>
        {section.additionalRecommendation && (
          <p style={contentTextStyle}>
            {section.additionalRecommendation}
          </p>
        )}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen p-5">
      {/* Main Container */}
      <div className="max-w-6xl mx-auto bg-white  flex flex-col gap-6 p-10 mb-2 ">

        {/* Header Section */}
        <div className="flex items-center justify-between gap-2 border-b-4 border-blue-900">
          <h1
            style={headerTextStyle}
          >
            {reportSummaryData.header.mainTitle}
          </h1>
          <p
            className="text-lg text-gray-600 m-0"
            style={subHeadingTextStyle}
          >
            {reportSummaryData.header.headerSubtext}
          </p>
        </div>

        {/* Executive Summary Section */}
        <div className="bg-white">
          <div
            className="text-2xl font-semibold text-teal-600 mb-4"
            style={headingTextStyle}
          >
            {reportSummaryData.executiveSummary.title}
          </div>
          <p
            className="text-lg leading-relaxed text-gray-700 mb-6"
            style={contentTextStyle}
          >
            {reportSummaryData.executiveSummary.content}
          </p>
        </div>

        {/* Analysis and Recommendations Header */}
        <div className="bg-white">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={headingTextStyle}
          >
            {reportSummaryData.analysisAndRecommendations.title}
          </div>

          {/* Render all analysis sections */}
          {reportSummaryData.analysisAndRecommendations.sections.map((section, index) => 
            renderAnalysisSection(section, index)
          )}
        </div>

        {/* Key Focus Areas for 2025 */}
        <div className="bg-white pb-5">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={headingTextStyle}
          >
            {reportSummaryData.keyFocusAreas.title}
          </div>
          <p
            className="text-lg leading-relaxed text-gray-700"
            style={contentTextStyle}
          >
            {reportSummaryData.keyFocusAreas.content}
          </p>
        </div>

        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>
          <p>
            {reportSummaryData.disclaimer}
          </p>
        </div>

      </div>
    </div>
  );
};

export default ReportSummary;