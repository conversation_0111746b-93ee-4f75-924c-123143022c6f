import * as React from "react";
import Button from "@mui/material/Button";
import { Box, Grid, Chip } from "@mui/material";
import { styled } from "@mui/material/styles";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import ButtonGroup from "@mui/material/ButtonGroup";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogActions from "@mui/material/DialogActions";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import { Formik, Field, Form } from "formik";
import Swal from "sweetalert2";
import { Country, State, City } from "country-state-city";
import AddPhotoAlternateOutlinedIcon from "@mui/icons-material/AddPhotoAlternateOutlined";
import { update } from "../../../services/company";
import { CircularProgress } from "@mui/material";
import { AvailableMarkets } from "../../../services/markets";
import PersonAddAltOutlinedIcon from "@mui/icons-material/PersonAddAltOutlined";
import { getDefaultUsersList } from "../../../services/settings";
import SharedUserSettingsModal from "./ShareUsersModal";
import * as Yup from "yup";

const BootstrapDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));

export default function EditCompany({ isOpen, onClose, companyData, onSave }) {
  const [data, setData] = React.useState({});
  const [logoPreview, setLogoPreview] = React.useState("");
  const [countries, setCountries] = React.useState([]);
  const [states, setStates] = React.useState([]);
  const [cities, setCities] = React.useState([]);
  const [logoUpdated, setLogoUpdated] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [isDragging, setIsDragging] = React.useState(false);

  const [location, setLocation] = React.useState({});
  const [isCreatedModal, setIsCreatedModal] = React.useState(false);
  const handleOpenModal = () => {
    setOpenModal(true);
    getUsersList(1, 10, "", addedUsers);
  };
  const handleCloseModal = () => setOpenModal(false);
  const [openModal, setOpenModal] = React.useState(false);
  const [addedUsers, setAddedUsers] = React.useState([]);

  const handleAddUser = (newUser) => {
    setAddedUsers((prevUsers) => {
      if (prevUsers?.some((u) => u.id === newUser.id)) return prevUsers; // Avoid duplicates
      return [...prevUsers, newUser]; // Append new user
    });
  };

  const handleRemoveUser = (user) => {
    setAddedUsers((prevUsers) => prevUsers.filter((u) => u !== user));
  };
  const handleClose = () => {
    onClose();
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const payload = {
        ...values,
        addedUsers: addedUsers.map((user) => user.id),
        country: location.country
          ? countries.find((c) => c.isoCode === location.country)?.name
          : "",
        state: location.state
          ? states.find((s) => s.isoCode === location.state)?.name
          : "",
        city: location.city || "",
        logo: logoUpdated ? values?.logo : null,
        market: values?.market || "",
      };
      const company_updated = await update(companyData.id, payload);
      if (company_updated?.data?.statusCode === 200) {
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Company updated",
        });
        onSave(company_updated.data.company);
        handleClose();
      }
    } catch (err) {
      Swal.fire({
        icon: "error",
        title: "Error!",
        text: err.message || "something went wrong.",
      });
    } finally {
      setLoading(false);
    }
  };
  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .required("Company name is required")
      .min(2, "Company name must be at least 2 characters"),
    fiscal_year_end: Yup.date().required("Fiscal year end is required"),
    description: Yup.string()
      .required("Critical company info is required")
      .min(10, "Critical company info must be at least 10 characters"),
    naics: Yup.string().required("NAICS code is required"),
  });
  const initialValues = {
    name: companyData?.name || "",
    fiscal_year_end: companyData?.fiscal_year_end
      ? new Date(companyData.fiscal_year_end).toISOString().split("T")[0]
      : "",
    country: companyData?.country,
    city: companyData?.city,
    state: companyData?.state,
    naics: companyData?.naics || "",
    description: companyData?.description || "",
    logo: companyData?.logo || "",
    market: companyData?.market || "",
    addedUsers: companyData?.users,
  };

  const handleDelete = (user) => {
    setAddedUsers((prevUsers) => prevUsers.filter((u) => u.id !== user.id));
  };

  React.useEffect(() => {
    const initializeLocationData = async () => {
      if (!companyData) return;

      // Get all countries
      const allCountries = Country.getAllCountries();
      setCountries(allCountries);

      // Find the country that matches companyData
      const selectedCountry = allCountries.find(
        (country) => country.name === companyData.country
      );

      if (selectedCountry) {
        // Get states for selected country
        const allStates = State.getStatesOfCountry(selectedCountry.isoCode);
        // Find the state that matches companyData
        const selectedState = allStates.find(
          (state) => state.name === companyData.state
        );

        // Get cities if both country and state are found
        let allCities = [];
        if (selectedState) {
          allCities = City.getCitiesOfState(
            selectedCountry.isoCode,
            selectedState.isoCode
          );

          // Filter out any city names that include the word "county" (case insensitive)
          allCities = allCities.filter(
            (city) => !city.name.toLowerCase().includes("county")
          );
        }

        // Update all state at once to avoid race conditions
        setStates(allStates);
        setCities(allCities);
        setLocation({
          country: selectedCountry.isoCode,
          state: selectedState?.isoCode || "",
          city: companyData.city || "",
        });
      }

      // Set logo preview if exists
      if (companyData.logo) {
        setLogoPreview(companyData.logo);
      }
    };
    setAddedUsers(companyData?.users);
    initializeLocationData();
  }, [companyData]);

  const handleCountryChange = (e) => {
    const selectedCountryIsoCode = e.target.value;
    const allStates = State.getStatesOfCountry(selectedCountryIsoCode);
    // Reset state and city when country changes
    setStates(allStates);
    setCities([]);
    setLocation({
      country: selectedCountryIsoCode,
      state: "",
      city: "",
    });
  };

  const handleStateChange = (e) => {
    const selectedStateIsoCode = e.target.value;

    // If null/0 index is selected, clear the state
    if (selectedStateIsoCode === "0") {
      handleClearState();
      return;
    }
    const allCities = City.getCitiesOfState(
      location.country,
      selectedStateIsoCode
    );

    const filteredCities = allCities.filter(
      (city) => !city.name.toLowerCase().includes("county")
    );
    setCities(filteredCities);
    setLocation((prev) => ({
      ...prev,
      state: selectedStateIsoCode,
      city: "",
    }));
  };

  const handleCityChange = (e) => {
    const selectedCity = e.target.value;

    // If 'None' is selected, clear the city
    if (selectedCity === "none") {
      handleClearCity();
      return;
    }

    setLocation((prev) => ({
      ...prev,
      city: selectedCity,
    }));
  };

  const handleClearState = () => {
    setLocation((prev) => ({
      ...prev,
      state: "",
      city: "",
    }));
    setCities([]);
  };

  const handleClearCity = () => {
    setLocation((prev) => ({
      ...prev,
      city: "",
    }));
  };

  const handleFileChange = (event, setFieldValue) => {
    const file = event.target.files[0];
    if (file) {
      setLogoPreview(URL.createObjectURL(file));

      // Convert file to Base64 string
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result.split(",")[1]; // Extract the Base64 portion
        // You can set this value to the state or pass it to the payload
        setFieldValue("logo", base64String);
        setLogoUpdated(true);
      };
      reader.onerror = (error) => {
        console.error("Error reading file:", error);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDrop = (event, setFieldValue) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    handleFileChange({ target: { files: [file] } }, setFieldValue);
    setIsDragging(false);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const getUsersList = async (
    page = 1,
    pageSize = 10,
    search = "",
    usersAdded = []
  ) => {
    const payload = {
      page,
      pageSize,
      search,
      users: usersAdded?.map((userAdded) => userAdded?.id),
      isUserAssociation: true,
      companyId: companyData?.id,
    };
    const users = await getDefaultUsersList(payload);
    if (users?.data?.statusCode === 200) {
      setData(users?.data);
    }
  };

  return (
    <BootstrapDialog
      fullWidth
      maxWidth="md"
      onClose={handleClose}
      aria-labelledby="customized-dialog-title"
      open={isOpen}
    >
      <DialogTitle
        sx={{ m: 0, p: 2 }}
        id="customized-dialog-title"
        className="font-bold"
      >
        Edit Company
        <Typography variant="body2" color="textSecondary" gutterBottom>
          Fill up the following fields to update a company profile.
        </Typography>
      </DialogTitle>
      <IconButton
        aria-label="close"
        onClick={handleClose}
        sx={(theme) => ({
          position: "absolute",
          right: 8,
          top: 8,
          color: theme.palette.grey[500],
        })}
      >
        <CloseIcon />
      </IconButton>

      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={validationSchema}
      >
        {({ setFieldValue, values, errors, touched }) => (
          <Form>
            <DialogContent dividers>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Company Name
                  {touched.name && Boolean(errors.name) ? (
                    <span className="text-red-500">*</span>
                  ) : (
                    "*"
                  )}
                </Typography>
                <Field
                  as={TextField}
                  fullWidth
                  margin="dense"
                  label="e.g. Linear"
                  name="name"
                  variant="outlined"
                  error={touched.name && Boolean(errors.name)}
                  helperText={touched.name && errors.name}
                />
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Location
                </Typography>
                <div
                  style={{
                    display: "flex",
                    gap: "20px",
                    marginTop: "10px",
                    flexGrow: 1,
                  }}
                >
                  <ButtonGroup fullWidth aria-label="Basic button group">
                    <Select
                      fullWidth
                      onChange={handleCountryChange}
                      value={location.country || ""}
                      displayEmpty
                    >
                      <MenuItem value="" disabled>
                        Select Country
                      </MenuItem>
                      {countries.map((country) => (
                        <MenuItem key={country.isoCode} value={country.isoCode}>
                          {country.name}
                        </MenuItem>
                      ))}
                    </Select>

                    <Select
                      fullWidth
                      value={location.state || ""}
                      onChange={handleStateChange}
                      displayEmpty
                      disabled={!location.country}
                    >
                      <MenuItem value="" disabled>
                        Select State
                      </MenuItem>
                      <MenuItem value="0">None</MenuItem>
                      {states.map((state) => (
                        <MenuItem key={state.isoCode} value={state.isoCode}>
                          {state.name}
                        </MenuItem>
                      ))}
                    </Select>

                    <Select
                      fullWidth
                      value={location.city || ""}
                      onChange={handleCityChange}
                      displayEmpty
                      disabled={!location.state}
                    >
                      <MenuItem value="" disabled>
                        Select City
                      </MenuItem>
                      <MenuItem value="none">None</MenuItem>
                      {cities.map((city) => (
                        <MenuItem key={city.name} value={city.name}>
                          {city.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </ButtonGroup>
                </div>
              </Box>

              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Fiscal Year End
                  {touched.fiscal_year_end &&
                  Boolean(errors.fiscal_year_end) ? (
                    <span className="text-red-500">*</span>
                  ) : (
                    "*"
                  )}
                </Typography>
                <Field
                  as={TextField}
                  fullWidth
                  margin="normal"
                  label="select date"
                  name="fiscal_year_end"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  error={
                    touched.fiscal_year_end && Boolean(errors.fiscal_year_end)
                  }
                  helperText={touched.fiscal_year_end && errors.fiscal_year_end}
                />
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Naics
                  {touched.naics && Boolean(errors.naics) ? (
                    <span className="text-red-500">*</span>
                  ) : (
                    "*"
                  )}
                </Typography>
                <Field
                  as={TextField}
                  fullWidth
                  margin="normal"
                  label="Naics"
                  name="naics"
                  placeholder="Type here"
                  error={touched.naics && Boolean(errors.naics)}
                  helperText={touched.naics && errors.naics}
                />
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Market
                </Typography>
                <div
                  style={{
                    display: "flex",
                    gap: "20px",
                    marginTop: "20px",
                    flexGrow: 1,
                  }}
                >
                  <Select
                    fullWidth
                    displayEmpty
                    name="market"
                    value={values.market}
                    onChange={(e) => setFieldValue("market", e.target.value)}
                  >
                    <MenuItem value="" disabled>
                      Select Market
                    </MenuItem>
                    {AvailableMarkets?.map((market) => (
                      <MenuItem key={market} value={market}>
                        {market}
                      </MenuItem>
                    ))}
                  </Select>
                </div>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Critical Company Info
                  {touched.description && Boolean(errors.description) ? (
                    <span className="text-red-600">*</span>
                  ) : (
                    "*"
                  )}
                </Typography>
                <Field
                  as={TextField}
                  fullWidth
                  margin="normal"
                  label="Critical company info"
                  name="description"
                  placeholder="Type here"
                  multiline
                  rows={3}
                  error={touched.description && Boolean(errors.description)}
                  helperText={touched.description && errors.description}
                />
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                {/* Company Logo */}
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Company logo
                </Typography>
                {!logoPreview ? (
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      background: "#EFF3F6",
                      padding: "12px 14px",
                      borderRadius: "50%",
                      width: "64px",
                      height: "64px",
                      overflow: "hidden",
                    }}
                  >
                    <AddPhotoAlternateOutlinedIcon fontSize="large" />
                  </Box>
                ) : (
                  <img
                    src={logoPreview}
                    alt="Uploaded logo"
                    style={{
                      width: "64px",
                      height: "64px",
                      objectFit: "cover",
                      borderRadius: "50%",
                    }}
                  />
                )}

                {/* Upload Button */}
                <Box
                  component="label"
                  onDrop={(event) => handleDrop(event, setFieldValue)}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  sx={{
                    flexGrow: 1,
                    marginTop: "8px",
                    marginLeft: "2px",
                    padding: "10px 20px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    textAlign: "center",
                    textTransform: "none",
                    border: isDragging ? "2px solid grey" : "1px solid #e0e0e0",
                    borderRadius: "4px",
                    cursor: "pointer",
                    backgroundColor: isDragging ? "#f5f5f5" : "transparent",
                    borderColor: isDragging ? "#0078d4" : "#e0e0e0",
                    backgroundColor: isDragging ? "#f5f5f5" : "transparent",
                    borderColor: isDragging ? "#0078d4" : "#e0e0e0",
                    "&:hover": {
                      borderColor: "#0078d4",
                    },
                  }}
                >
                  <Typography variant="body1" sx={{ whiteSpace: "nowrap" }}>
                    <span style={{ color: "#033BD7", fontWeight: 600 }}>
                      Click to upload
                    </span>{" "}
                    or drag and drop
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      marginTop: "8px",
                      color: "#2E3A44",
                      fontWeight: "400",
                    }}
                  >
                    SVG, PNG, JPG, or GIF (max. 800x400px)
                  </Typography>
                  <input
                    hidden
                    accept="image/*"
                    multiple
                    type="file"
                    onChange={(event) => handleFileChange(event, setFieldValue)}
                  />
                </Box>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  width: "100%",
                  flex: 1,
                  marginTop: "20px",
                }}
              >
                <Typography
                  variant="body1"
                  sx={{ minWidth: "200px", fontWeight: 500 }}
                >
                  Share With
                </Typography>

                <Box
                  sx={{
                    flex: 1,
                    display: "flex",
                    alignItems: "center",
                    gap: "20px",
                    marginLeft: "-10px",
                  }}
                >
                  <Button
                    onClick={handleOpenModal}
                    sx={{
                      ml: 1,
                      borderRadius: "50%",
                      background: "#EFF3F6",
                      padding: "30px",
                      width: "50px",
                      height: "50px",
                      position: "relative",
                    }}
                  >
                    <PersonAddAltOutlinedIcon
                      fontSize="large"
                      sx={{ color: "black" }}
                    />
                  </Button>

                  <Box
                    sx={{
                      display: "flex",
                      flexWrap: "wrap",
                      gap: 1,
                      minHeight: "40px",
                      maxHeight: "140px",
                      overflowY: "auto",
                      p: 1,
                      border: "1px solid #ccc",
                      borderRadius: "5px",
                      width: "100%",
                    }}
                  >
                    {addedUsers?.map((user) => (
                      <Chip
                        key={user.id}
                        label={user.username}
                        onDelete={() => handleDelete(user)}
                        sx={{ m: 0.5 }}
                        variant="outlined"
                      />
                    ))}
                  </Box>

                  <SharedUserSettingsModal
                    open={openModal}
                    data={data}
                    onClose={handleCloseModal}
                    onAddUser={handleAddUser}
                    addedUsers={addedUsers}
                    isCreatedModal={isCreatedModal}
                    fetchUsers={getUsersList}
                  />
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Grid container spacing={2} className="p-3">
                <Grid item xs={6}>
                  <Button onClick={handleClose} variant="outlined" fullWidth>
                    Cancel
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    fullWidth
                  >
                    {loading ? (
                      <CircularProgress size={20} sx={{ color: "white" }} />
                    ) : (
                      "Save"
                    )}
                  </Button>
                </Grid>
              </Grid>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </BootstrapDialog>
  );
}
