import { prisma } from '../db/prisma.js';
import {
  deleteFileFromS3,
  generateSignedUrl,
  getFileAsBase64,
} from '../utils/aws-s3.js';
import {
  sendEmailWithTemplate,
  sendEmail,
  downloadReportEmailTemplate,
  editReportEmailTemplate,
} from '../utils/sendEmail.js';
import { uploadFileOnS3 } from '../utils/aws-s3.js';
import {
  ReportRequestStatus,
  RequestType,
  WeeklyReportTypes,
} from '../enums/report.enum.js';
import path from 'path';
import { HttpStatusCode } from '../enums/error.enum.js';
import { getCompanyByID } from './company.service.js';
import {
  createErrorResponse,
  createSuccessResponse,
} from '../utils/response.js';
import pkg from '@prisma/client';
import { executeQuery, reportQueries } from './dbService.js';
import { ErrorHandler } from '../utils/errorHandler.js';
import { flattenFinancialData } from '../utils/parseData.js';
const { ReportStatus, FileType } = pkg;

export const requestTypeLabels = {
  monthly: 'ProfitPulse (Monthly)',
  benchmark: 'KPITrack (Benchmark)',
  gaap: 'GAAP Align',
  csfa: 'FinCheck (Current State)',
  weekly: 'FlowCast (13 Week)',
  deepsight : 'DeepSight'
};

export const processReportRequest = async (payload, user) => {
  try {
    const company = await getCompanyByID(Number(payload.companyId));
    if (!company) {
      return createErrorResponse(
        HttpStatusCode.NOT_FOUND,
        'Company Not Found!',
      );
    }

    switch (payload.request_type) {
      case RequestType.WEEKLY:
        return await handle13WeeksRequest(payload, company, user);
      case RequestType.MONTHLY:
      case RequestType.CSFA:
        return await handleMonthlyOrCSFARequest(payload, company, user);
      case RequestType.DEEPSIGHT:
        return await handleDeepSightRequest(payload, company, user);
      case RequestType.BENCHMARK:
        return await handleBenchmarkRequest(payload, company, user);
      case RequestType.GAAP_ANALYZER:
        return await handleGAAPAnalyzerRequest(payload, company, user);
      default:
        return createErrorResponse(
          HttpStatusCode.BAD_REQUEST,
          'Invalid Request Type',
        );
    }
  } catch (err) {
    console.log({ err });
    return createErrorResponse(
      err.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Error while processing report request',
    );
  }
};
const createReportRequest = async (payload) => {
  const payloadToSend = await prepareRequestPayload(payload);

  return await prisma.$transaction(async (prisma) => {
    return await prisma.reportRequest.create({ data: payloadToSend });
  });
};
const handleGAAPAnalyzerRequest = async (payload, company, user) => {
  if (payload.files.length === 0) {
    return createErrorResponse(
      HttpStatusCode.BAD_REQUEST,
      'GAAP Align files are required!',
    );
  }

  const reportRequestCreated = await createReportRequest(payload);
  try {
    const emailTemplate = await gaapAnalyzerReportEmailTemplate(
      payload,
      company,
      reportRequestCreated?.id,
      user.email,
    );

    await sendEmailWithTemplate(
      process.env.SUPPORT_EMAIL,
      `${requestTypeLabels[payload?.request_type]} Report Request`,
      emailTemplate.body,
      emailTemplate.attachments,
    );

    return createSuccessResponse(
      HttpStatusCode.CREATED,
      'Report request created and email sent successfully!',
    );
  } catch (err) {
    console.error('Error sending email:', err);
    await rollbackReportRequest(reportRequestCreated?.id);
    throw createErrorResponse(
      err.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to send email, transaction rolled back!',
    );
  }
};

const handleBenchmarkRequest = async (payload, company, user) => {
  const payloadToSend = await prepareRequestPayload(payload);

  const reportRequestCreated = await prisma.$transaction(async (prisma) => {
    return await prisma.reportRequest.create({ data: payloadToSend });
  });

  try {
    const emailTemplate = await benchmarkReportEmailTemplate(
      payload,
      company,
      reportRequestCreated?.id,
      user.email,
    );
    await sendEmailWithTemplate(
      process.env.SUPPORT_EMAIL,
      `${requestTypeLabels[payload?.request_type]} Report Request`,
      emailTemplate.body,
      emailTemplate.attachments,
    );

    return createSuccessResponse(
      HttpStatusCode.CREATED,
      'Report request created and email sent successfully!',
    );
  } catch (err) {
    console.error('Error sending email:', err);
    await rollbackReportRequest(reportRequestCreated?.id);
    return createErrorResponse(
      err.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to send email, transaction rolled back!',
    );
  }
};

const handle13WeeksRequest = async (payload, company, user) => {
  const payloadToSend = await prepareRequestPayload(payload);

  const reportRequestCreated = await prisma.$transaction(async (prisma) => {
    return await prisma.reportRequest.create({ data: payloadToSend });
  });

  try {
    const emailTemplate = await weeklyReportEmailTemplate(
      payload,
      company,
      reportRequestCreated,
      user?.email,
    );
    await sendEmailWithTemplate(
      process.env.SUPPORT_EMAIL,
      `${requestTypeLabels[payload.request_type]} Report Request`,
      emailTemplate.body,
      emailTemplate.attachments,
    );

    return createSuccessResponse(
      HttpStatusCode.CREATED,
      'Report request created and email sent successfully!',
      reportRequestCreated,
    );
  } catch (err) {
    console.error('Error sending email:', err);
    await rollbackReportRequest(reportRequestCreated.id);
    return createErrorResponse(
      err.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to send email, transaction rolled back!',
    );
  }
};

const rollbackReportRequest = async (reportRequestId) => {
  await prisma.reportRequest.delete({
    where: { id: reportRequestId },
  });
};

const handleDeepSightRequest = async (payload, company, user) => {
  const payloadToSend = await prepareRequestPayload(payload);
  const reportRequestCreated = await prisma.$transaction(async (prisma) => {
    return await prisma.reportRequest.create({ data: payloadToSend });
  });
  try {
    const emailTemplate = await monthlyOrCSFAReportEmailTemplate(
      payload,
      company,
      reportRequestCreated?.id,
      user?.email,
    );
    await sendEmailWithTemplate(
      process.env.SUPPORT_EMAIL,
      `${requestTypeLabels[payload?.request_type]} Report Request`,
      emailTemplate.body,
      emailTemplate.attachments,
    );

    return createSuccessResponse(
      HttpStatusCode.CREATED,
      'Report request created and email sent successfully!',
      reportRequestCreated,
    );
  } catch (err) {
    console.log('error in handleMonthlyOrCSFARequest', err);
    await rollbackReportRequest(reportRequestCreated.id);
    return createErrorResponse(
      err.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to send email, transaction rolled back!',
    );
  }
};

const handleMonthlyOrCSFARequest = async (payload, company, user) => {
  const payloadToSend = await prepareRequestPayload(payload);
  const reportRequestCreated = await prisma.$transaction(async (prisma) => {
    return await prisma.reportRequest.create({ data: payloadToSend });
  });
  try {
    const emailTemplate = await monthlyOrCSFAReportEmailTemplate(
      payload,
      company,
      reportRequestCreated?.id,
      user?.email,
    );
    await sendEmailWithTemplate(
      process.env.SUPPORT_EMAIL,
      `${requestTypeLabels[payload?.request_type]} Report Request`,
      emailTemplate.body,
      emailTemplate.attachments,
    );

    return createSuccessResponse(
      HttpStatusCode.CREATED,
      'Report request created and email sent successfully!',
      reportRequestCreated,
    );
  } catch (err) {
    console.log('error in handleMonthlyOrCSFARequest', err);
    await rollbackReportRequest(reportRequestCreated.id);
    return createErrorResponse(
      err.statusCode || HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to send email, transaction rolled back!',
    );
  }
};

const monthlyOrCSFAReportEmailTemplate = async (
  payload,
  company,
  reportRequestId,
  userEmail,
) => {
  const chart_of_accounts = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'CHART_OF_ACCOUNTS',
  );
  const trial_balance = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'TRIAL_BALANCE',
  );

  let latest_chart_account = {};
  let latest_trial_balance_file = {};
  let base64TBFile = '';
  let base64File = '';
  let company_logo = '';

  if (chart_of_accounts?.length > 0) {
    latest_chart_account = chart_of_accounts[0];
    base64File = await getFileAsBase64(latest_chart_account?.file_key);
  }

  if (trial_balance?.length > 0) {
    latest_trial_balance_file = trial_balance[0];
    base64TBFile = await getFileAsBase64(latest_trial_balance_file?.file_key);
  }

  if (company?.logo) {
    company_logo = await getFileAsBase64(company?.logo);
  }

  return {
    body: `
            <h1>${requestTypeLabels[payload.request_type]} Report Request</h1>
            <p><strong>Report Request ID:</strong> ${reportRequestId}</p>
            <p><strong>Date Requested:</strong> ${payload.date}</p>
            <p><strong>Requester Email:</strong> ${userEmail}</p>
            <p><strong>Company Name:</strong> ${company.name}</p>
            <p><strong>Company ID:</strong> ${company.id}</p>
            <p><strong>NAICS Code:</strong> ${company?.naics}</p>
            ${company?.country || company?.state || company?.city ? `<p><strong>Location:</strong> ${company?.country || ''}, ${company?.state || ''}, ${company?.city || ''}</p>` : ''}  
            <p><strong>Pertinent Information:</strong> ${company.description}</p>
            <p><strong>Attached Files:</strong></p>
            <ul>
                ${company && company_logo ? `<li>Company Logo File: ${company.name}</li>` : ''}
                ${latest_chart_account && base64File ? `<li>Chart of Accounts File: ${latest_chart_account?.file_name}</li>` : ''}
                ${latest_trial_balance_file && base64TBFile ? `<li>Trial Balance File: ${latest_trial_balance_file?.file_name}</li>` : ''}
            </ul>
        `,
    attachments: [
      ...(latest_chart_account && base64File
        ? [
            {
              filename: latest_chart_account?.file_name,
              content: base64File,
              encoding: 'base64',
            },
          ]
        : []),
      ...(latest_trial_balance_file && base64TBFile
        ? [
            {
              filename: latest_trial_balance_file?.file_name,
              content: base64TBFile,
              encoding: 'base64',
            },
          ]
        : []),
      ...(company_logo && company
        ? [
            {
              filename: company?.name,
              content: company_logo,
              encoding: 'base64',
            },
          ]
        : []),
    ],
  };
};

const prepareRequestPayload = async (payload) => {
  const formattedDate = prepareRequestName();
  return {
    name: `${payload.request_type}-projection-${formattedDate}`,
    status: ReportRequestStatus.PENDING,
    request_type: payload.request_type,
    companyId: Number(payload.companyId),
    date_requested: new Date(payload.date),
    cashflow: !!(payload.request_type === RequestType.WEEKLY),
  };
};

const prepareRequestName = () => {
  const currentDate = new Date();
  const options = { year: 'numeric', month: 'long' };
  return currentDate.toLocaleDateString('en-US', options);
};

const weeklyReportEmailTemplate = async (
  payload,
  company,
  request,
  userEmail,
) => {
  const transactionFile = payload.files.find(
    (file) => file.type === WeeklyReportTypes.TRANSACTION,
  );
  const previousCashFlowFile = payload.files.find(
    (file) => file.type === WeeklyReportTypes.PREVIOUS_CASH_FLOW,
  );
  const arFile = payload.files.find(
    (file) => file.type === WeeklyReportTypes.AR,
  );
  const apFile = payload.files.find(
    (file) => file.type === WeeklyReportTypes.AP,
  );
  const trial_balance = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'TRIAL_BALANCE',
  );

  //get chart of accounts
  const chart_of_accounts = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'CHART_OF_ACCOUNTS',
  );
  let latest_chart_account = {};
  let base64File = '';

  let latest_trial_balance_file = {};
  let base64TBFile = '';

  if (trial_balance?.length > 0) {
    latest_trial_balance_file = trial_balance[0];
    base64TBFile = await getFileAsBase64(latest_trial_balance_file?.file_key);
  }

  if (chart_of_accounts?.length > 0) {
    latest_chart_account = chart_of_accounts[0];
    base64File = await getFileAsBase64(latest_chart_account?.file_key);
  }

  const attachments = [
    {
      filename: transactionFile.name,
      content: transactionFile.content,
      encoding: 'base64',
    },
    ...(latest_trial_balance_file && base64TBFile
      ? [
          {
            filename: latest_trial_balance_file?.file_name,
            content: base64TBFile,
            encoding: 'base64',
          },
        ]
      : []),
    ...(latest_chart_account && base64File
      ? [
          {
            filename: latest_chart_account?.file_name,
            content: base64File,
            encoding: 'base64',
          },
        ]
      : []), // FIXED HERE
    ...(arFile
      ? [
          {
            filename: arFile.name,
            content: arFile.content,
            encoding: 'base64',
          },
        ]
      : []),
    ...(apFile
      ? [
          {
            filename: apFile.name,
            content: apFile.content,
            encoding: 'base64',
          },
        ]
      : []),
    ...(previousCashFlowFile && previousCashFlowFile.content
      ? [
          {
            filename: previousCashFlowFile?.name,
            content: previousCashFlowFile.content,
            encoding: 'base64',
          },
        ]
      : []),
  ];

  return {
    body: `
            <h1> ${requestTypeLabels[request.request_type]} Report Request</h1>
            <p><strong>Requestor Email:</strong> ${userEmail}</p>
            <p><strong>Requested ID:</strong> ${request?.id}</p>
            <p><strong>Date Requested:</strong> ${payload.date}</p>
            <p><strong>Company Name:</strong> ${company.name}</p>
            <p><strong>Company ID:</strong> ${company.id}</p>
            <p><strong>NAICS Code:</strong> ${company.naics}</p>
            ${company?.country || company?.state || company?.city ? `<p><strong>Location:</strong> ${company?.country || ''}, ${company?.state || ''}, ${company?.city || ''}</p>` : ''}  
            <p><strong>Pertinent Information:</strong> ${company.description}</p>
            <p><strong>Attached Files:</strong></p>
            <ul>
                <li>Transaction File: ${transactionFile.name}</li>
                ${previousCashFlowFile ? `<li>Previous Cash Flow File: ${previousCashFlowFile.name}</li>` : ''}
                ${arFile ? `<li>AR File: ${arFile.name}</li>` : ''}
                ${apFile ? `<li>AP File: ${apFile.name}</li>` : ''}
                ${latest_chart_account && base64File ? `<li>Chart of Accounts File: ${latest_chart_account?.file_name}</li>` : ''}
                ${latest_trial_balance_file && base64TBFile ? `<li>Trial Balance File: ${latest_trial_balance_file?.file_name}</li>` : ''}
                </ul>
        `,
    attachments,
  };
};

const benchmarkReportEmailTemplate = async (
  payload,
  company,
  requestId,
  userEmail,
) => {
  const chart_of_accounts = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'CHART_OF_ACCOUNTS',
  );
  const trial_balance = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'TRIAL_BALANCE',
  );

  let latest_chart_account = {};
  let latest_trial_balance_file = {};
  let base64TBFile = '';
  let base64File = '';
  let company_logo = '';

  if (chart_of_accounts?.length > 0) {
    latest_chart_account = chart_of_accounts[0];
    base64File = await getFileAsBase64(latest_chart_account?.file_key);
  }

  if (trial_balance?.length > 0) {
    latest_trial_balance_file = trial_balance[0];
    base64TBFile = await getFileAsBase64(latest_trial_balance_file?.file_key);
  }

  if (company?.logo) {
    company_logo = await getFileAsBase64(company?.logo);
  }

  console.log('user email', userEmail);

  return {
    body: `
            <h1>${requestTypeLabels[payload.request_type]} Report Request</h1>
            <p><strong>Report Request ID:</strong> ${requestId}</p>
            <p><strong>Requester Email:</strong> ${userEmail}</p>
            <p><strong>Date Requested:</strong> ${payload.date}</p>
            <p><strong>Company Name:</strong> ${company.name}</p>
            <p><strong>Company ID:</strong> ${company.id}</p>
            <p><strong>NAICS Code:</strong> ${company.naics}</p>
            ${company?.country || company?.state || company?.city ? `<p><strong>Location:</strong> ${company?.country || ''}, ${company?.state || ''}, ${company?.city || ''}</p>` : ''}
            <p><strong>Pertinent Information:</strong> ${company.description}</p>
            <p><strong>Attached Files:</strong></p>
            <ul>
                ${company && company_logo ? `<li>Company Logo File:  ${company?.name}</li>` : ''}
                ${latest_chart_account && base64File ? `<li>Chart of Accounts File: ${latest_chart_account?.file_name}</li>` : ''}
                ${latest_trial_balance_file && base64TBFile ? `<li>Trial Balance File: ${latest_trial_balance_file?.file_name}</li>` : ''}
            </ul>
        `,
    attachments: [
      ...(latest_chart_account && base64File
        ? [
            {
              filename: latest_chart_account?.file_name,
              content: base64File,
              encoding: 'base64',
            },
          ]
        : []),
      ...(latest_trial_balance_file && base64TBFile
        ? [
            {
              filename: latest_trial_balance_file?.file_name,
              content: base64TBFile,
              encoding: 'base64',
            },
          ]
        : []),
      ...(company_logo && company
        ? [
            {
              filename: company?.name,
              content: company_logo,
              encoding: 'base64',
            },
          ]
        : []),
    ],
  };
};

const gaapAnalyzerReportEmailTemplate = async (
  payload,
  company,
  requestId,
  userEmail,
) => {
  const chart_of_accounts = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'CHART_OF_ACCOUNTS',
  );
  const trial_balance = company?.companyFiles?.filter(
    (companyFile) => companyFile?.file_type === 'TRIAL_BALANCE',
  );

  const balance_sheet = payload.files.find(
    (file) => file.type === 'balance_sheet',
  );
  const income_statement = payload.files.find(
    (file) => file.type === 'income_statement',
  );

  let latest_chart_account = {};
  let latest_trial_balance_file = {};
  let base64TBFile = '';
  let base64File = '';
  let company_logo = '';

  if (chart_of_accounts?.length > 0) {
    latest_chart_account = chart_of_accounts[0];
    base64File = await getFileAsBase64(latest_chart_account?.file_key);
  }

  if (trial_balance?.length > 0) {
    latest_trial_balance_file = trial_balance[0];
    base64TBFile = await getFileAsBase64(latest_trial_balance_file?.file_key);
  }

  if (company?.logo) {
    company_logo = await getFileAsBase64(company?.logo);
  }

  return {
    body: `
            <h1>${requestTypeLabels[payload.request_type]} Report Request</h1>
            <p><strong>Report Request ID:</strong> ${requestId}</p>
            <p><strong>Requester Email:</strong> ${userEmail}</p>
            <p><strong>Date Requested:</strong> ${payload.date}</p>
            <p><strong>Company Name:</strong> ${company.name}</p>
            <p><strong>Company ID:</strong> ${company.id}</p>
            <p><strong>NAICS Code:</strong> ${company.naics}</p>
            ${company?.country || company?.state || company?.city ? `<p><strong>Location:</strong> ${company?.country || ''}, ${company?.state || ''}, ${company?.city || ''}</p>` : ''}  
            <p><strong>Pertinent Information:</strong> ${company.description}</p>
            <p><strong>Attached Files:</strong></p>
            <ul>
                ${company && company_logo ? `<li>Company Logo File:  ${company?.name}</li>` : ''}
                ${latest_chart_account && base64File ? `<li>Chart of Accounts File: ${latest_chart_account?.file_name}</li>` : ''}
                ${latest_trial_balance_file && base64TBFile ? `<li>Trial Balance File: ${latest_trial_balance_file?.file_name}</li>` : ''}
                ${balance_sheet && balance_sheet?.content ? `<li>Balance Sheet File: ${balance_sheet.name}</li>` : ''}
                ${income_statement && income_statement?.content ? `<li>Income Statement File: ${income_statement.name}</li>` : ''}
            </ul>
        `,
    attachments: [
      ...(latest_chart_account && base64File
        ? [
            {
              filename: latest_chart_account?.file_name,
              content: base64File,
              encoding: 'base64',
            },
          ]
        : []),
      ...(latest_trial_balance_file && base64TBFile
        ? [
            {
              filename: latest_trial_balance_file?.file_name,
              content: base64TBFile,
              encoding: 'base64',
            },
          ]
        : []),
      ...(company_logo && company
        ? [
            {
              filename: company?.name,
              content: company_logo,
              encoding: 'base64',
            },
          ]
        : []),
      ...(balance_sheet && balance_sheet?.content
        ? [
            {
              filename: balance_sheet?.name,
              content: balance_sheet?.content,
              encoding: 'base64',
            },
          ]
        : []),
      ...(income_statement && income_statement?.content
        ? [
            {
              filename: income_statement?.name,
              content: income_statement?.content,
              encoding: 'base64',
            },
          ]
        : []),
    ],
  };
};

export const reportRequests = async (companyId) => {
  try {
    const requests = await prisma.reportRequest.findMany({
      where: {
        companyId,
      },
      orderBy: {
        id: 'desc',
      },
      include: {
        documents: {
          orderBy: {
            createdAt: 'desc', // or updatedAt if applicable
          },
          take: 1, // Limit to the latest document
        },
      },
    });

    for (const request of requests) {
      if (request?.documents?.length > 0) {
        for (const document of request.documents) {
          if (document.file_key) {
            document.file_key = await generateSignedUrl(document.file_key);
          }
        }
      }
    }
    return {
      success: true,
      statusCode: 200,
      data: requests,
    };
  } catch (err) {
    console.log({ err });
    throw {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while reportRequests',
    };
  }
};

export const downloadReports = async (request_id) => {
  try {
    const requests = await prisma.reportRequest.findMany({
      where: {
        id: request_id,
      },
      include: {
        documents: {
          select: {
            file_key: true,
          },
        },
      },
    });

    for (let request of requests) {
      if (request?.documents?.length > 0) {
        for (let document of request?.documents) {
          if (document.file_key) {
            document.file_key = await generateSignedUrl(document.file_key);
          }
        }
      }
    }

    return {
      success: true,
      statusCode: 200,
      data: requests,
    };
  } catch (err) {
    console.log({ err });
    throw {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while download Reports',
    };
  }
};

export const deleteReportRequest = async (request_id) => {
  try {
    const result = await prisma.$transaction(async (prisma) => {
      const report_request = await prisma.reportRequest.findFirst({
        where: { id: request_id },
        include: {
          documents: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      });

      if (!report_request) {
        throw new Error('Report request not found.');
      }

      if (
        report_request.documents?.length > 0 &&
        report_request.documents[0]?.file_key
      ) {
        console.log(`Deleting file: ${report_request.documents[0].file_key}`);
        await deleteFileFromS3(report_request.documents[0].file_key);
      }

      // Delete the report request
      const deletedRequest = await prisma.reportRequest.delete({
        where: { id: request_id },
      });

      return deletedRequest;
    });

    return {
      success: true,
      statusCode: 200,
      request: result,
    };
  } catch (err) {
    console.log({ err });
    return {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while deleting report request.',
    };
  }
};

export const saveEditedReport = async (req) => {
  try {
    const request_id = Number(req.params.id);
    const { user } = req;
    const { file_name, updated_text } = req.body;
    const result = await prisma.$transaction(async (prisma) => {
      const request = await prisma.reportRequest.findFirst({
        where: { id: request_id },
        include: {
          Company: true,
          documents: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      });

      if (!request) {
        throw new Error('Request not found!');
      }
      if (request.status !== ReportStatus.edit)
        throw new Error('Report is not in an editable state.');

      const updatedStatus = updated_text
        ? ReportStatus.pending
        : ReportStatus.download;
      const updatedRequest = await prisma.reportRequest.update({
        where: { id: request_id },
        data: { status: updatedStatus },
      });

      if (updated_text) {
        await sendEditedReportEmail(request, user, file_name, updated_text);
      } else {
        await sendDownloadReportEmail(req.user, request);
      }

      return updatedRequest;
    });
    return {
      success: true,
      statusCode: 200,
      request: result,
    };
  } catch (err) {
    console.log({ err });
    return {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while saving edited report request.',
    };
  }
};

const sendEditedReportEmail = async (
  request,
  user,
  file_name,
  updated_text,
) => {
  const emailData = {
    body: `
            <h1>${request.request_type} Report Request</h1>
            <p><strong>Requestor Email:</strong> ${user?.email}</p>
            <p><strong>Request ID:</strong> ${request.id}</p>
            <p><strong>Company ID:</strong> ${request.Company?.id}</p>
            <p><strong>Company Name:</strong> ${request.Company?.name}</p>
            <p><strong>File Name:</strong> ${file_name}</p>
            <p><strong>Updated Text Fields:</strong> ${updated_text}</p>`,
  };

  console.log('Sending email for edited PDF...');
  await sendEmailWithTemplate(
    process.env.SUPPORT_EMAIL,
    `${requestTypeLabels[request.request_type]} Edited PDF content`,
    emailData.body,
  );
};

const sendDownloadReportEmail = async (user, reportRequest) => {
  const fileKey = encodeURIComponent(reportRequest.documents[0].file_key);
  const payload = {
    recipientName: user?.username,
    reportName: requestTypeLabels[reportRequest?.request_type],
    companyName: reportRequest?.Company?.name,
    fileName: reportRequest.documents[0].file_name,
    fileUrl: `${process.env.BASE_URL}/api/v1/report/generate-report-link/${fileKey}`,
  };

  const body = downloadReportEmailTemplate(payload);
  const subject = `${reportRequest?.Company?.name} ${requestTypeLabels[reportRequest?.request_type]} Ready to Download`;
  await sendEmailWithTemplate(user?.email, subject, body);
};

export const generateReportLink = async (fileName) => {
  try {
    return await generateSignedUrl(fileName);
  } catch (err) {
    console.log({ err });
    return {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while generating report link.',
    };
  }
};

export const getReportById = async (id) => {
  try {
    const report_request = await prisma.reportRequest.findFirst({
      where: {
        id: Number(id),
      },
      include: {
        documents: {
          orderBy: {
            createdAt: 'desc', // or updatedAt if applicable
          },
          take: 1, // Limit to the latest document
        },
      },
    });

    report_request.documents.map(async (document) => {
      document.file_key = await generateSignedUrl(document.file_key);
    });

    return report_request;
  } catch (error) {
    return {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while getReportById',
    };
  }
};

const findReportRequestByID = async (request_id) => {
  return await prisma.reportRequest.findFirst({
    where: { id: request_id },
    include: {
      documents: {
        orderBy: { createdAt: 'desc' },
        take: 1,
      },
      Company: {
        include: {
          User: true, // Include user information
        },
      },
    },
  });
};

export const uploadNonCashflowFile = async (req) => {
  try {
    const companyId = parseInt(req.body.company_id);
    const request_id = parseInt(req.params.id);

    // Check if a file was uploaded
    if (!req.file) {
      return {
        status: 400,
        message: 'No file uploaded',
        success: false,
      };
    }

    const fileUploaded = await fileUploader(req.file);
    const report_request = await findReportRequestByID(request_id);

    return await prisma.$transaction(async (ps) => {
      if (report_request && validateRequest(report_request?.request_type)) {
        const fileBuffer = req.file.buffer; // File buffer from memory storage
        const base64File = fileBuffer.toString('base64');
        const buffer = Buffer.from(base64File, 'base64');
        const sizeInBytes = buffer.length;

        //update status
        const payload = {
          file_type: FileType.NON_CASH_FLOW,
          file_name: req.file.originalname,
          file_key: fileUploaded?.Key,
          size: JSON.stringify(sizeInBytes),
          requestId: request_id,
          companyId: parseInt(companyId),
        };

        const request_document_created = await ps.document.create({
          data: payload,
        });
        if (!request_document_created) {
          throw new Error('Unable to create documents entry');
        }

        const request_status_update = await ps.reportRequest.update({
          where: { id: payload.requestId },
          data: { status: ReportStatus.edit },
        });

        await sendEmailOnEditStatus(report_request);
        return {
          status: 200,
          success: true,
          message: 'File uploaded successfully, Please check you Email',
          file: request_status_update,
        };
      }
    });
  } catch (err) {
    console.log({ err });
    return {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while uploadNonCashflowFile',
    };
  }
};

export const uploadCashflowFile = async (req) => {
  try {
    const companyId = Number(req.body.company_id);
    const request_id = Number(req.params.id);

    if (!req.file) {
      return {
        status: 400,
        message: 'No file uploaded',
        success: false,
      };
    }

    const fileUploaded = await fileUploader(req.file);
    const fileBuffer = req.file.buffer; // File buffer from memory storage
    const base64File = fileBuffer.toString('base64');
    const buffer = Buffer.from(base64File, 'base64');
    const sizeInBytes = buffer.length;

    //update status
    const payload = {
      file_type: FileType.NON_CASH_FLOW,
      file_name: req.file.originalname,
      file_key: fileUploaded?.Key,
      size: JSON.stringify(sizeInBytes),
      requestId: request_id,
      companyId,
    };

    const result = await prisma.$transaction(async (ps) => {
      const request_document_created = await ps.document.create({
        data: payload,
      });
      if (!request_document_created) {
        throw new Error('Unable to create documents entry');
      }

      const request_status_update = await ps.reportRequest.update({
        where: { id: payload.requestId },
        data: { status: ReportRequestStatus.DOWNLOAD },
      });

      return request_status_update;
    });

    const report = await findReportRequestByID(request_id);
    await sendDownloadReportEmail(report.Company?.User, report);

    return {
      status: 200,
      success: true,
      message: 'File uploaded successfully, Please check you Email',
      file: result,
    };
  } catch (err) {
    console.log({ err });
    return {
      success: false,
      statusCode: err.statusCode || 500,
      message: 'Error while upload Cashflow File',
    };
  }
};

const sendEmailOnEditStatus = async (report_request) => {
  const emailBody = editReportEmailTemplate({
    recipientName: report_request?.Company?.User?.username,
    reportName: requestTypeLabels[report_request?.request_type],
    companyName: report_request?.Company?.name,
    url: `${process.env.REACT_APP_FRONTEND_URL}/companies/${report_request?.Company?.id}/reports/${report_request?.id}/edit`,
  });

  const subject = `${report_request?.Company?.name} ${requestTypeLabels[report_request?.request_type]} Ready to edit`;
  await sendEmailWithTemplate(
    report_request?.Company?.User?.email,
    subject,
    emailBody,
  );
};

const validateRequest = (request_type) => {
  return [
    RequestType.MONTHLY,
    RequestType.GAAP_ANALYZER,
    RequestType.CSFA,
    RequestType.BENCHMARK,
  ].includes(request_type);
};

const fileUploader = async (file) => {
  const fileNameWithoutExtension = path.parse(file.originalname).name; // Extract filename without extension
  const uniqueFileName = `${Date.now()}-${fileNameWithoutExtension}`; // Create a unique filename without the extension   console.log("file name ---", fileName)

  const fileBuffer = file.buffer; // File buffer from memory storage
  const base64File = fileBuffer.toString('base64'); // Convert file buffer to base64
  const fileName = `/uploads/reports/${uniqueFileName}`; // Create a unique file name
  return await uploadFileOnS3(base64File, fileName);
};

export const saveEditedNonCashFlowReport = async (req) => {
  try {
    const companyId = req.body.company_id;
    const request_id = Number(req.params.id);

    // Check if a file was uploaded
    if (!req.file) {
      return {
        status: HttpStatusCode.NOT_ACCEPTABLE,
        success: false,
        message: 'No file uploaded',
      };
    }

    const fileUploaded = await fileUploader(req.file);
    const report_request = await findReportRequestByID(request_id);

    return await prisma.$transaction(async (ps) => {
      if (report_request && validateRequest(report_request?.request_type)) {
        const fileBuffer = req.file.buffer; // File buffer from memory storage
        const base64File = fileBuffer.toString('base64');
        const buffer = Buffer.from(base64File, 'base64');
        const sizeInBytes = buffer.length;

        //update status
        const payload = {
          file_type: FileType.NON_CASH_FLOW,
          file_name: req.file.originalname,
          file_key: fileUploaded?.Key,
          size: JSON.stringify(sizeInBytes),
          requestId: request_id,
          companyId: parseInt(companyId),
        };

        const request_document_created = await ps.document.create({
          data: payload,
        });
        if (!request_document_created) {
          return {
            status: HttpStatusCode.INTERNAL_SERVER_ERROR,
            success: false,
            message: 'Unable to update status in document entry',
          };
        }

        const request_status_update = await ps.reportRequest.update({
          where: { id: payload.requestId },
          data: { status: ReportStatus.download },
        });

        console.log('request_status_update ----', request_status_update);

        const report = await findReportRequestByID(request_id);
        await sendDownloadReportEmail(report.Company?.User, report);
        return {
          status: HttpStatusCode.OK,
          message: 'File uploaded successfully',
          success: true,
          data: request_status_update,
        };
      }
    });
  } catch (error) {
    console.log({ error });
    return {
      success: false,
      statusCode: error.statusCode || 500,
      message: 'Error while generating report link.',
    };
  }
};

export const saveText = async (requestId, updatedText) => {
  try {
    let report = await prisma.reportRequest.update({
      where: {
        id: requestId,
      },
      data: {
        text: updatedText,
      },
      select: {
        id: true,
        text: true,
      },
    });

    return {
      success: true,
      statusCode: 200,
      report,
    };
  } catch (error) {
    return {
      success: false,
      status: 200,
      statusCode: 400,
      message: 'Error While updating the text',
    };
  }
};

export const getRealmIdFromCompany = async (companyId) => {
  // Input validation
  if (!companyId) {
    throw new ErrorHandler('Company ID is required', 400);
  }

  // Convert to integer if it's a string
  const parsedCompanyId = parseInt(companyId);
  if (isNaN(parsedCompanyId)) {
    throw new ErrorHandler('Invalid company ID format', 400);
  }

  // Check if company exists
  const companyData = await prisma.company.findUnique({
    where: { id: parsedCompanyId },
    select: {
      id: true,
      qboRealmID: true,
      qboConnectionStatus: true,
      name: true,
    },
  });

  if (!companyData) {
    throw new ErrorHandler(`Company ID ${companyId} not found`, 404);
  }

  // Check if company has qboRealmID
  if (!companyData.qboRealmID) {
    throw new ErrorHandler(
      `No realm ID found for company ID ${companyId}`,
      404,
    );
  }

  // Optional: Check if QuickBooks connection is still active
  if (companyData.qboConnectionStatus !== 'CONNECTED') {
    throw new ErrorHandler(
      `QuickBooks connection not active for company ${companyData.name}`,
      400,
    );
  }

  return {
    success: true,
    realmId: companyData.qboRealmID,
    company: companyData,
  };
};

export async function calculateAllReportMetrics(realmId) {
  try {
    // Validate reportQueries exists and is not empty
    if (
      !reportQueries ||
      typeof reportQueries !== 'object' ||
      Object.keys(reportQueries).length === 0
    ) {
      throw new ErrorHandler('No report queries configured', 500);
    }

    const results = {};
    const errors = [];
    const queryEntries = Object.entries(reportQueries);

    // Execute queries with individual error handling
    const promises = queryEntries.map(async ([key, queryFunc]) => {
      try {
        if (typeof queryFunc !== 'function') {
          errors.push({
            query: key,
            error: 'Invalid query function',
            code: 'INVALID_QUERY_FUNCTION',
          });
          return;
        }

        const res = await executeQuery(queryFunc, realmId, key);

        if (res?.success) {
          results[key] = res.data;
        } else {
          errors.push({
            query: key,
            error: res?.error || 'Unknown query error',
            code: res?.code || 'QUERY_FAILED',
            details: res?.details,
          });
        }
      } catch (queryError) {
        console.error(`Error executing query ${key}:`, queryError);
        errors.push({
          query: key,
          error: queryError.message || 'Query execution failed',
          code: 'QUERY_EXECUTION_ERROR',
          details:
            process.env.NODE_ENV === 'development'
              ? queryError.stack
              : undefined,
        });
      }
    });

    await Promise.all(promises);

    // Log results for debugging (consider removing in production)
    if (process.env.ENVIRONMENT === 'development') {
      console.log(
        'Financial reports results: successfulQueries',
        Object.keys(results).length,
      );
      if (errors.length > 0) {
        console.log('Financial reports errors:', errors);
      }
    }

    // Determine overall success
    const hasResults = Object.keys(results).length > 0;
    const hasErrors = errors.length > 0;
    let finalData = await flattenFinancialData(results);

    return {
      success: hasResults,
      results: finalData,
      errors: errors ?? [],
      summary: {
        totalQueries: queryEntries.length,
        successfulQueries: Object.keys(results).length,
        failedQueries: errors.length,
        hasPartialFailure: hasResults && hasErrors,
      },
    };
  } catch (error) {
    console.error('Unexpected error in getAllFinancialReports:', error);

    if (error instanceof ErrorHandler) {
      throw error;
    }
    // Otherwise, wrap it in ErrorHandler
    throw new ErrorHandler(
      error.message ||
        'Unexpected error occurred while generating financial reports',
      error.statusCode || 500,
    );
  }
}
