import Joi from 'joi';

export const financialValidation = {
  // Validation for comprehensive financial data endpoint
  comprehensiveData: Joi.object().keys({
    companyId: Joi.number().integer().positive().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a number',
      'number.integer': 'Company ID must be an integer',
      'number.positive': 'Company ID must be positive'
    }),
    accountIds: Joi.array().items(Joi.string()).optional().messages({
      'array.base': 'Account IDs must be an array',
      'string.base': 'Each account ID must be a string'
    }),
    year: Joi.number().integer().min(2000).max(2100).optional().messages({
      'number.base': 'Year must be a number',
      'number.integer': 'Year must be an integer',
      'number.min': 'Year must be 2000 or later',
      'number.max': 'Year must be 2100 or earlier'
    }),
    month: Joi.number().integer().min(1).max(12).optional().messages({
      'number.base': 'Month must be a number',
      'number.integer': 'Month must be an integer',
      'number.min': 'Month must be between 1 and 12',
      'number.max': 'Month must be between 1 and 12'
    })
  }),

  // Validation for account-specific financial data endpoint
  accountSpecificData: Joi.object().keys({
    companyId: Joi.number().integer().positive().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a number',
      'number.integer': 'Company ID must be an integer',
      'number.positive': 'Company ID must be positive'
    }),
    accountIds: Joi.array().items(Joi.string()).min(1).required().messages({
      'any.required': 'Account IDs are required',
      'array.base': 'Account IDs must be an array',
      'array.min': 'At least one account ID is required',
      'string.base': 'Each account ID must be a string'
    })
  }),

  // Validation for company ID parameter
  companyIdParam: Joi.object().keys({
    companyId: Joi.number().integer().positive().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a number',
      'number.integer': 'Company ID must be an integer',
      'number.positive': 'Company ID must be positive'
    })
  }),

  // Validation for accounts with limit query
  accountsWithLimit: Joi.object().keys({
    companyId: Joi.number().integer().positive().required().messages({
      'any.required': 'Company ID is required',
      'number.base': 'Company ID must be a number',
      'number.integer': 'Company ID must be an integer',
      'number.positive': 'Company ID must be positive'
    }),
    limit: Joi.number().integer().min(1).max(100).optional().default(10).messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    })
  })
};
