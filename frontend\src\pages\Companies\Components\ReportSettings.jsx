import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Switch,
  Card,
  Stack,
  Divider,
  TextField,
  Chip,
  IconButton,
  Tooltip,
  Button,
  useTheme,
  alpha,
  Snackbar,
  Alert,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  CircularProgress,
} from '@mui/material';

import {
  TuneOutlined as SettingsIcon,
  InfoOutlined as InfoIcon,
  CheckCircleOutlined as CheckIcon,
  SaveOutlined as SaveIcon,
} from '@mui/icons-material';

import {
  getContentSettingsByReportType,
  updateContentSettings
} from '../../../services/contentSettings';

const ReportSettings = ({ companyId }) => {
  const theme = useTheme();
  const [settings, setSettings] = useState({
    reportType: 'DEEPSIGHT', // Added report type state
    incomeStatement: {
      incomeSummary: true,
      netIncome: true,
      grossProfitMargin: true,
      netProfitMargin: true,
      roaAndRoe: true,
    },
    prompt: "",
  });

  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  // Load existing settings on component mount
  useEffect(() => {
    const loadSettings = async () => {
      if (!companyId) return;

      try {
        setIsLoading(true);
        const response = await getContentSettingsByReportType(companyId, 'DEEPSIGHT');

        if (response.data.success && response.data.data && response.data.data.length > 0) {
          const data = response.data.data;
          setSettings(prev => ({
            ...prev,
            incomeStatement: data.chartSettings || prev.incomeStatement,
            prompt: data.promptDescription || prev.prompt,
          }));
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        setErrorMessage('Failed to load settings');
        setShowError(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, [companyId]); // Depend on companyId

  const handleSwitchChange = (category, field) => (event) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [field]: event.target.checked
      }
    }));
    setHasChanges(true);
  };

  const handlePromptChange = (event) => {
    setSettings(prev => ({
      ...prev,
      prompt: event.target.value
    }));
    setHasChanges(true);
  };

  // Added handler for report type change
  const handleReportTypeChange = async (event) => {
    if (!companyId) return;

    const newReportType = event.target.value;
    setSettings(prev => ({
      ...prev,
      reportType: newReportType
    }));

    // Load settings for the new report type
    try {
      setIsLoading(true);
      const response = await getContentSettingsByReportType(companyId, newReportType);

      if (response.data.success && response.data.data && response.data.data.length > 0) {
        const data = response.data.data;
        setSettings(prev => ({
          ...prev,
          incomeStatement: data.chartSettings || {
            incomeSummary: true,
            netIncome: true,
            grossProfitMargin: true,
            netProfitMargin: true,
            roaAndRoe: true,
          },
          prompt: data.promptDescription || '',
        }));
      } else {
        // Reset to defaults if no data found
        setSettings(prev => ({
          ...prev,
          incomeStatement: {
            incomeSummary: true,
            netIncome: true,
            grossProfitMargin: true,
            netProfitMargin: true,
            roaAndRoe: true,
          },
          prompt: '',
        }));
      }
      setHasChanges(false);
    } catch (error) {
      console.error('Error loading settings for report type:', error);
      setErrorMessage('Failed to load settings for selected report type');
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!companyId) return;

    setIsSaving(true);

    try {
      const payload = {
        chartSettings: settings.incomeStatement,
        promptDescription: settings.prompt,
      };

      await updateContentSettings(companyId, settings.reportType, payload);

      setHasChanges(false);
      setShowSuccess(true);
    } catch (error) {
      console.error('Error saving settings:', error);
      setErrorMessage(error.response?.data?.message || 'Failed to save settings');
      setShowError(true);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCloseSuccess = () => {
    setShowSuccess(false);
  };

  const handleCloseError = () => {
    setShowError(false);
    setErrorMessage('');
  };

  const settingsOptions = [
    {
      key: 'incomeSummary',
      label: 'Income Summary'
    },
    {
      key: 'netIncome',
      label: 'Net Income'
    },
    {
      key: 'grossProfitMargin',
      label: 'Gross Profit Margin'
    },
    {
      key: 'netProfitMargin',
      label: 'Net Profit Margin'
    },
    {
      key: 'roaAndRoe',
      label: 'ROA and ROE'
    },
  ];

  // Report type options
  const reportTypeOptions = [
    {
      value: 'DEEPSIGHT',
      label: 'Deepsight'
    },
    // Future report types can be added here
  ];

  if (isLoading) {
    return (
      <Box sx={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '400px'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header Section */}
      <Box sx={{ mb: 2 }}>


        {/* Report Type Dropdown */}
        <Box sx={{  maxWidth: '300px', mb: 3 }}>
          <FormControl fullWidth size="small">
            <InputLabel 
              id="report-type-label"
              sx={{
                fontSize: '1rem',
                fontWeight: 500,
                color: 'text.secondary',
              }}
            >
              Report Type
            </InputLabel>
            <Select
              labelId="report-type-label"
              id="report-type-select"
              value={settings.reportType}
              label="Report Type"
              onChange={handleReportTypeChange}
              sx={{
                '& .MuiSelect-select': {
                  fontSize: '0.875rem',
                  padding : '15px 10px',
                  fontWeight: 500,
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'grey.400',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#1976d2',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#1976d2',
                },
              }}
            >
              {reportTypeOptions.map((option) => (
                <MenuItem 
                  key={option.value} 
                  value={option.value}
                  sx={{
                    fontSize: '0.875rem',
                    fontWeight: 500,
                  }}
                >
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

  

      {/* Report Components Section */}
      <Card elevation={0}>
        {/* Report Components Section - Simplified */}
        <Box sx={{ mb: 3,  maxWidth: '300px' }}>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              fontSize: '1rem',
            }}
          >
            Report Components
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: 'text.secondary' }}
          >
            Select which financial metrics to include in your analysis
          </Typography>

          <Box sx={{ maxWidth: '600px' }}>
            {settingsOptions.map((option, index) => (
              <Box 
                key={option.key}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  py: 1,
                  borderBottom: index < settingsOptions.length - 1 ? '1px solid' : 'none',
                  borderBottomColor: 'grey.200',
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 500,
                      color: 'text.primary',
                      fontSize: '0.95rem',
                      lineHeight: 1.3,
                    }}
                  >
                    {option.label}
                  </Typography>
                </Box>

                <Switch
                  checked={settings.incomeStatement[option.key]}
                  onChange={handleSwitchChange('incomeStatement', option.key)}
                  inputProps={{ 'aria-label': 'controlled' }}
                  sx={{ ml: 2 }}
                />
              </Box>
            ))}
          </Box>
        </Box>
      </Card>

          {/* Custom Analysis Prompt Section with Fieldset Style */}
      <Card
        elevation={0}
        sx={{
          mb: 3,
          width :  600,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            borderColor: '#1976d2',
            backgroundColor: alpha('#1976d2', 0.02),
          },
        }}
      >
        {/* Fieldset-style container */}
        <Box
          component="fieldset"
          sx={{
            border: '2px solid',
            borderColor: 'grey.400',
            borderRadius: 1,
            // padding: 2,
            margin: 0,
            position: 'relative',
            backgroundColor: 'white',
            transition: 'border-color 0.2s ease-in-out',
            '&:focus-within': {
              borderColor: '#1976d2',
            },
            '&:focus-within legend': {
              color: '#1976d2',
            },
          }}
        >
          {/* Legend label */}
          <Box
            component="legend"
            sx={{
              fontSize: '0.875rem',
              fontWeight: 500,
              fontFamily: '"Roboto","Helvetica","Arial",sans-serif',
              color: 'text.secondary',
              padding: '0 2px',
              marginLeft: 1,
              transition: 'color 0.2s ease-in-out',
            }}
          >
            Prompt
          </Box>
          
          {/* Text area */}
          <TextField
            multiline
            rows={4}
            value={settings.prompt}
            onChange={handlePromptChange}
            placeholder="Type here"
            fullWidth
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                '& fieldset': {
                  border: 'none', // Remove default border since fieldset provides it
                },
                '&:hover fieldset': {
                  border: 'none',
                },
                '&.Mui-focused fieldset': {
                  border: 'none',
                },
              },
              '& .MuiInputBase-input': {
                fontSize: '0.875rem',
                lineHeight: 1.6,
                '&::placeholder': {
                  color: 'text.secondary',
                  opacity: 0.7,
                },
              },
            }}
          />
        </Box>
      </Card>

      {/* Save Button */}
      <Box sx={{ mt: 4, width: '10%', justifySelf: 'flex-start' }}>
        <Button
          variant="contained"
          size="medium"
          onClick={handleSave}
          disabled={!hasChanges || isSaving}
          fullWidth
          sx={{
            textTransform: 'none',
          }}
        >
          {isSaving ? 'SAVING...' : 'SAVE'}
        </Button>
      </Box>

      {/* Success Snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={4000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseSuccess}
          severity="success"
          variant="filled"
          sx={{
            backgroundColor: '#1976d2',
            '& .MuiAlert-icon': {
              color: 'white',
            },
          }}
        >
          Settings saved successfully!
        </Alert>
      </Snackbar>

      {/* Error Snackbar */}
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseError}
          severity="error"
          variant="filled"
        >
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ReportSettings;