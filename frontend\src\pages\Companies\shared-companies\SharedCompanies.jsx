import React, { useState, useCallback } from "react";
import { Button } from "@mui/material";
import SharedCompaniesList from "./SharedCompaniesList";
import SharedCompaniesModal from "./SharedCompaniesModal";
import { getDefaultUsersList } from "../../../services/settings";

const SharedCompanies = () => {
  const [openModal, setOpenModal] = useState(false);
  const [data, setData] = useState({});
  const [addedUsers, setAddedUsers] = useState([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const handleOpenModal = () => {
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleAddUser = (newUsers) => {
    setAddedUsers((prevUsers) => [...prevUsers, ...newUsers]);
  };

  const getUsersList = useCallback(
    async (page = 1, pageSize = 10, search = "") => {
      setIsLoading(true);
      try {
        const payload = {
          page,
          pageSize,
          search,
          isUserAssociation: false,
          users: [],
        };

        const users = await getDefaultUsersList(payload);
        if (users?.data?.statusCode === 200) {
          setData(users?.data);
        }
      } catch (error) {
        console.error("Error fetching users list:", error);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const handleRefresh = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  return (
    <div className="px-20 py-5">
      <div className="flex justify-between item-center my-7">
        <h1 className="text-2xl font-bold ml-7 ">Share Companies With</h1>
        <Button variant="contained" size="medium" onClick={handleOpenModal}>
          Add
        </Button>
      </div>

      <SharedCompaniesList
        addedUsers={addedUsers}
        refreshTrigger={refreshTrigger}
        onRefresh={handleRefresh}
      />

      <SharedCompaniesModal
        open={openModal}
        data={data}
        onClose={handleCloseModal}
        onAddUser={handleAddUser}
        addedUsers={addedUsers}
        fetchUsers={getUsersList}
        isLoading={isLoading}
      />
    </div>
  );
};

export default SharedCompanies;
