# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Testing
/coverage

# Production
/build
/dist
/out

# Environment variables
.env
*.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.zip

# IDE specific files
.idea
.vscode
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Debug logs
logs
*.log

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# React specific
/.next/
/out/

# Misc
.cache/
frontend/node_modules
backend/node_modules

frontend/build
