import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
import { customTemplate } from "../helpers/templateHelper.js";
const baseUrl = `/api/v1/templates`;

describe("Update Template API", function () {
    let templateId
   before(async()=>{
    templateId =  await customTemplate()
   })

    it("should update the template and documents successfully", async function () {

        const response = await request(app)
            .put(`${baseUrl}/${templateId}`)
            .attach('files', Buffer.from('Test file content'), 'sample.pdf')
            .field('description', 'Template description');            

        expect(response.status).to.equal(200);
        expect(response._body.success).to.be.true;
        expect(response._body.message).to.equal("Template and documents updated successfully");
    });

    it("should return 404 if template not found", async function () {

        const response = await request(app).put(`${baseUrl}/01`).send({ name: "New Template" });
        

        expect(response._body.statusCode).to.equal(404);
        expect(response._body.message).to.equal("Template not found");
    });

    it("should handle internal server errors", async function () {

        const response = await request(app)
        .put(`${baseUrl}/${templateId}`)
        .attach('files', Buffer.from('Test file content'), 'sample.pdf')
        .field('description', 'Template description');            

        expect(response._body.statusCode).not.equal(500);
        expect(response._body.message).not.equal("Database error");
    });
});
