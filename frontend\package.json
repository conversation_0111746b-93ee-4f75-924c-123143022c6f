{"name": "fintuition-ui", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.1", "@mui/material": "^6.2.1", "@mui/x-date-pickers": "^7.23.3", "@react-pdf-viewer/core": "^3.12.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "apexcharts": "^5.2.0", "country-state-city": "^3.2.1", "diff": "^7.0.0", "diff-match-patch": "^1.0.5", "dotenv": "^16.5.0", "formik": "^2.4.6", "html-diff": "^0.0.4", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^2.5.1", "lucide-react": "^0.469.0", "moment": "^2.30.1", "node-fetch": "^3.3.2", "pdfjs-dist": "^4.10.38", "pdfkit": "^0.17.1", "pdfmake": "^0.2.20", "react": "^18.3.1", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-pdf": "^9.2.1", "react-quill": "^2.0.0", "react-router": "^7.0.2", "react-router-dom": "^7.0.2", "react-rte": "^0.16.5", "react-scripts": "^5.0.1", "sweetalert2": "^11.15.1", "web-vitals": "^2.1.4", "worker-loader": "^3.0.8", "xlsx": "^0.18.5", "yup": "^1.6.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/prop-types": "^15.7.14", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "postcss": "^8.4.49", "tailwindcss": "^3.4.16"}}