import React from "react";
import { useNavigate } from "react-router-dom";
import logo from "../assets/breadcrumbslogo.png";

const Breadcrumb = ({ companyName }) => {
  const navigate = useNavigate();

  const handleHomeClick = () => {
    navigate("/dashboard");
  };

  return (
    <nav className="bg-white">
      <div className="container  px-12 py-3">
        <ol className="flex items-center space-x-2 text-sm text-gray-500">
          <li
            className="text-gray-500 flex items-center cursor-pointer hover:text-gray-700"
            onClick={handleHomeClick}
          >
            <img src={logo} className="mr-2" width="20px" alt="logo" />
            <span>My Companies</span>
          </li>

          {companyName && (
            <li className="flex items-center">
              <span> / </span>
              <span
                className="ml-3 text-[#033BD7] text-[14px] font-semibold bg-blue-200 rounded-md"
                style={{ padding: "3px 12px" }}
              >
                {companyName}
              </span>
            </li>
          )}
        </ol>
      </div>
    </nav>
  );
};

export default Breadcrumb;
