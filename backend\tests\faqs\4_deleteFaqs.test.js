import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
import { getFaqsId } from "../helpers/faqsHelper.js";
const baseUrl = `/api/v1/faqs`;
const randomIds = '999,1000'
const InvaidIds = 'invalid,123'

describe('DELETE /api/faqs', () => {
    let faqIds;

    before(async () => {
    

        faqIds = await getFaqsId();
        faqIds = faqIds.map((faq) => faq.id).join(',')
        
        
    });

    it('should delete FAQs successfully', async () => {
        const res = await request(app)
            .delete(`${baseUrl}/${faqIds}`)
            .expect(200);
        
        expect(res._body.success).to.be.true;
        expect(res._body.message).to.include('FAQs deleted successfully');
    });

    it('should return an error for invalid IDs', async () => {
        const res = await request(app)
            .delete(`${baseUrl}/${InvaidIds}`)
        

        expect(res._body.success).to.be.false;
        expect(res._body.status).to.equal(400);
        expect(res._body.message).to.equal('Invalid IDs. Ensure all IDs are numeric.');
    });
    

    it('should return success if no matching IDs are found', async () => {
        const res = await request(app)
            .delete(`${baseUrl}/${randomIds}`)
        
        expect(res._body.success).to.be.true;
        expect(res._body.message).to.include('0 FAQs deleted successfully.');
    });
});
