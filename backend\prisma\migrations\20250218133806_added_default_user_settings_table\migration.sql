-- CreateTable
CREATE TABLE "DefaultUsersSettings" (
    "id" SERIAL NOT NULL,
    "username" TEXT NOT NULL,
    "sharedBy" INTEGER,
    "sharedWith" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "DefaultUsersSettings_pkey" PRIMARY KEY ("id")
);

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE "DefaultUsersSettings" ADD CONSTRAINT "DefaultUsersSettings_sharedBy_fkey" FOREIGN KEY ("sharedBy") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DefaultUsersSettings" ADD CONSTRAINT "DefaultUsersSettings_sharedWith_fkey" FOREIGN KEY ("sharedWith") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
