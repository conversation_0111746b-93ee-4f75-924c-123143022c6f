# Financial Data API Documentation

This API provides comprehensive financial data for companies, including Chart of Accounts, Trial Balance, Profit & Loss, Balance Sheet, AR Aging, and AP Aging data.

**Important**: The API takes the **first 2 account records from Profit & Loss table** and returns their data from all financial tables (Trial Balance, Balance Sheet) with account names bound to each record. AR and AP data is returned in full (company-wide).

## Authentication

All endpoints require authentication via <PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### 1. Get Comprehensive Financial Data

**GET** `/api/financial/comprehensive/:companyId`

Retrieves comprehensive financial data for a company including Chart of Accounts, Trial Balance, Profit & Loss, Balance Sheet, AR Aging, and AP Aging. **Takes first 2 account records from Profit & Loss table** and returns their complete financial data with account names. AR and AP data is returned in full for the entire company.

#### Parameters

- `companyId` (path, required): Company ID

#### Query Parameters

- `accountIds[]` (optional): Array of account IDs to filter data
- `year` (optional): Year filter (2000-2100)
- `month` (optional): Month filter (1-12)

#### Example Request

```
GET /api/financial/comprehensive/123?accountIds=1&accountIds=2&year=2024&month=12
```

#### Response

```json
{
  "success": true,
  "statusCode": 200,
  "data": {
    "company": {
      "id": 123,
      "name": "Company Name",
      "qboConnectionStatus": "CONNECTED"
    },
    "summary": {
      "totalAccounts": 2,
      "targetAccountIds": ["1", "2"],
      "accountSelectionMethod": "first_2_from_profit_loss",
      "accountsWithTrialBalanceData": ["1", "2"],
      "accountsWithProfitLossData": ["1", "2"],
      "accountsWithBalanceSheetData": ["1", "2"],
      "totalTrialBalanceRecords": 24,
      "totalProfitLossRecords": 24,
      "totalBalanceSheetRecords": 24,
      "totalARRecords": 25,
      "totalAPRecords": 30,
      "dataConsistency": {
        "allAccountsHaveProfitLoss": true,
        "allAccountsHaveTrialBalance": true,
        "allAccountsHaveBalanceSheet": true
      },
      "lastSyncDates": {
        "trialBalance": "2024-01-15",
        "profitLoss": "2024-01-15",
        "balanceSheet": "2024-01-15",
        "arAging": "2024-01-15",
        "apAging": "2024-01-15"
      }
    },
    "chartOfAccounts": [...],
    "trialBalance": [...],
    "profitLoss": [...],
    "balanceSheet": [...],
    "arAging": [...],
    "apAging": [...]
  }
}
```

### 2. Get Financial Data by Specific Accounts

**POST** `/api/financial/by-accounts`

Retrieves financial data for specific account IDs.

#### Request Body

```json
{
  "companyId": 123,
  "accountIds": ["1", "2", "3"]
}
```

#### Response

Same structure as comprehensive endpoint but filtered to specified accounts.

### 3. Get Financial Summary

**GET** `/api/financial/summary/:companyId`

Retrieves only summary statistics and company information.

#### Parameters

- `companyId` (path, required): Company ID

#### Response

```json
{
  "success": true,
  "statusCode": 200,
  "data": {
    "company": {
      "id": 123,
      "name": "Company Name",
      "qboConnectionStatus": "CONNECTED"
    },
    "summary": {
      "totalAccounts": 50,
      "totalTrialBalanceRecords": 150,
      "totalProfitLossRecords": 100,
      "totalBalanceSheetRecords": 120,
      "totalARRecords": 25,
      "totalAPRecords": 30,
      "lastSyncDates": {...}
    }
  }
}
```

### 4. Get Accounts with Recent Data (Limited)

**GET** `/api/financial/accounts/:companyId`

Retrieves chart of accounts with associated financial data, limited to specified number of accounts.

#### Parameters

- `companyId` (path, required): Company ID

#### Query Parameters

- `limit` (optional, default: 10): Number of accounts to return (max: 100)

#### Example Request

```
GET /api/financial/accounts/123?limit=5
```

## Data Structures

### Chart of Accounts

```json
{
  "id": 1,
  "accountId": "1",
  "name": "Cash",
  "fullyQualifiedAccountName": "Cash",
  "type": "Bank",
  "accountSubTypeName": "Checking",
  "accountClassification": "Asset",
  "currentAccountBalance": 10000.0,
  "currencyCode": "USD",
  "isSubAccountFlag": false,
  "parentAccountQuickbooksId": null,
  "createdAt": "2024-01-01T00:00:00Z",
  "modifiedAt": "2024-01-15T00:00:00Z"
}
```

### Trial Balance (Enriched with Account Names)

```json
{
  "id": 1,
  "accountId": "1",
  "year": 2024,
  "month": 12,
  "monthEndDebitAmount": 5000.0,
  "monthEndCreditAmount": 0.0,
  "netChangeAmount": 5000.0,
  "accountName": "Cash",
  "accountType": "Bank",
  "accountClassification": "Asset",
  "createdAt": "2024-01-01T00:00:00Z",
  "modifiedAt": "2024-01-15T00:00:00Z"
}
```

### Profit & Loss (Enriched with Account Names)

```json
{
  "id": 1,
  "accountId": "1",
  "year": 2024,
  "month": 12,
  "amount": 1000.0,
  "currencyCode": "USD",
  "accountName": "Cash",
  "accountType": "Bank",
  "accountClassification": "Asset",
  "createdAt": "2024-01-01T00:00:00Z",
  "modifiedAt": "2024-01-15T00:00:00Z"
}
```

### Balance Sheet (Enriched with Account Names)

```json
{
  "id": 1,
  "accountId": "1",
  "year": 2024,
  "month": 12,
  "statementAmount": 10000.0,
  "currencyCode": "USD",
  "accountName": "Cash",
  "accountType": "Bank",
  "accountClassification": "Asset",
  "createdAt": "2024-01-01T00:00:00Z",
  "modifiedAt": "2024-01-15T00:00:00Z"
}
```

### AR/AP Aging

```json
{
  "id": 1,
  "year": 2024,
  "month": 12,
  "total": 5000.0,
  "createdAt": "2024-01-01T00:00:00Z",
  "modifiedAt": "2024-01-15T00:00:00Z"
}
```

## Error Responses

### 400 Bad Request

```json
{
  "success": false,
  "statusCode": 400,
  "message": "Validation error message"
}
```

### 401 Unauthorized

```json
{
  "success": false,
  "statusCode": 401,
  "message": "Unauthorized"
}
```

### 404 Not Found

```json
{
  "success": false,
  "statusCode": 404,
  "message": "Company not found or access denied"
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "statusCode": 500,
  "message": "Internal server error while fetching financial data"
}
```

## Usage Examples

### Frontend JavaScript

```javascript
import {
  getComprehensiveFinancialData,
  getFirst2AccountsFinancialData,
} from './services/financial';

// Get all financial data
const allData = await getComprehensiveFinancialData(123);

// Get data for first 2 accounts
const limitedData = await getFirst2AccountsFinancialData(123);

// Get data with filters
const filteredData = await getComprehensiveFinancialData(123, {
  year: 2024,
  month: 12,
  accountIds: ['1', '2'],
});
```

### cURL Examples

```bash
# Get comprehensive data
curl -H "Authorization: Bearer <token>" \
  "http://localhost:3000/api/financial/comprehensive/123"

# Get data for specific accounts
curl -X POST -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"companyId": 123, "accountIds": ["1", "2"]}' \
  "http://localhost:3000/api/financial/by-accounts"

# Get summary only
curl -H "Authorization: Bearer <token>" \
  "http://localhost:3000/api/financial/summary/123"
```
