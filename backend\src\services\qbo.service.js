// backend\src\services\qbo.service.js

import axios from 'axios';
import qs from 'qs';
import { prisma } from '../db/prisma.js';

// Input validation helpers
const validateCompanyId = (companyId) => {
  if (!companyId) {
    throw new Error('Company ID is required');
  }

  const parsed = parseInt(companyId);
  if (isNaN(parsed) || parsed <= 0) {
    throw new Error('Company ID must be a valid positive number');
  }

  return parsed;
};

const validateUserId = (userId) => {
  if (!userId) {
    throw new Error('User ID is required');
  }
  return userId;
};

const validateRealmId = (realmId) => {
  if (!realmId || typeof realmId !== 'string') {
    throw new Error('Realm ID must be a valid string');
  }
  return realmId;
};

const validateAuthCode = (code) => {
  if (!code || typeof code !== 'string') {
    throw new Error('Authorization code is required and must be a string');
  }
  return code;
};

const validateState = (state) => {
  if (!state) {
    throw new Error('State parameter is required');
  }

  try {
    const parsed = JSON.parse(state);
    if (!parsed.userId || !parsed.companyId) {
      throw new Error('State must contain userId and companyId');
    }
    return parsed;
  } catch (error) {
    throw new Error('Invalid state parameter format');
  }
};

// Environment variables
const clientId = process.env.QBO_CLIENT_ID;
const clientSecret = process.env.QBO_CLIENT_SECRET;
const redirectUri = process.env.QBO_REDIRECT_URI;
const tokenUrl = process.env.QBO_TOKEN_URL;
const authorizationBaseUrl = process.env.QBO_AUTHORIZATION_BASE_URL;

// Helper functions for environment-based URLs
const getQboApiBaseUrl = () => {
  return process.env.ENVIRONMENT === 'production'
    ? process.env.QBO_API_BASE_URL_PRODUCTION
    : process.env.QBO_API_BASE_URL_SANDBOX;
};

const getQboRevokeUrl = () => {
  return process.env.ENVIRONMENT === 'production'
    ? process.env.QBO_REVOKE_URL_PRODUCTION
    : process.env.QBO_REVOKE_URL_SANDBOX;
};

const getAuthorizationUrl = async (userId, companyId) => {
  try {
    const validUserId = validateUserId(userId);
    const validCompanyId = validateCompanyId(companyId);

    const state = JSON.stringify({
      userId: validUserId,
      companyId: validCompanyId,
    });

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: 'com.intuit.quickbooks.accounting',
      state: state,
    });

    return `${authorizationBaseUrl}?${params.toString()}`;
  } catch (error) {
    throw new Error(`Failed to generate authorization URL: ${error.message}`);
  }
};

const exchangeCodeForToken = async (code) => {
  try {
    const validCode = validateAuthCode(code);

    // Use env or fallback to default QBO token URL
    const effectiveTokenUrl =
      tokenUrl || 'https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer';

    const response = await axios.post(
      effectiveTokenUrl,
      qs.stringify({
        grant_type: 'authorization_code',
        code: validCode,
        redirect_uri: redirectUri,
      }),
      {
        headers: {
          Authorization:
            'Basic ' +
            Buffer.from(`${clientId}:${clientSecret}`).toString('base64'),
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );

    const token = response.data;

    // Validate response structure
    if (!token.access_token || !token.refresh_token) {
      throw new Error('Invalid token response from QuickBooks');
    }

    return {
      access_token: token.access_token,
      refresh_token: token.refresh_token,
      expires_in: token.expires_in || 3600, // Default 1 hour if not provided
      realmId: token.realmId,
    };
  } catch (error) {
      if (error.response?.status === 400) {
      throw new Error('Invalid authorization code or expired token');
    }
    if (error.response?.status === 401) {
      throw new Error('Authentication failed with QuickBooks');
    }
    throw new Error(`Token exchange failed: ${error.message}`);
  }
};

const validateIntegrationConstraints = async (realmId, companyId) => {
  try {
    const validRealmId = validateRealmId(realmId);
    const validCompanyId = validateCompanyId(companyId);

    // Use ensureCompanyExists for company existence check
    await ensureCompanyExists(validCompanyId);

    // Check CompanyId → already linked to different RealmId?
    const existingByCompanyId = await prisma.company.findFirst({
      where: { id: validCompanyId },
    });

    if (
      existingByCompanyId &&
      existingByCompanyId.qboRealmID &&
      existingByCompanyId.qboRealmID !== validRealmId
    ) {
      const error = new Error(
        `This company is already linked to QuickBooks company "${existingByCompanyId.qboCompanyName}").`,
      );
      error.name = 'COMPANY_ID_CONFLICT';
      error.cause = `This company is already linked to QuickBooks company "${existingByCompanyId.qboCompanyName}"`;
      throw error;
    }

    // Check RealmId → already linked to different company?
    const existingByRealmId = await prisma.company.findFirst({
      where: { qboRealmID: validRealmId },
    });

    if (existingByRealmId && existingByRealmId.id !== validCompanyId) {
      const error = new Error(
        'This QuickBooks account is already linked to another company.',
      );
      error.name = 'REALM_ID_CONFLICT';
      error.cause =
        'This QuickBooks account is already linked to another company.';
      throw error;
    }

    return { valid: true };
  } catch (error) {
    if (
      error.name === 'REALM_ID_CONFLICT' ||
      error.name === 'COMPANY_ID_CONFLICT'
    ) {
      throw error;
    }
    throw new Error(`Validation failed: ${error.message}`);
  }
};

const processCallback = async (code, realmId, state) => {
  let companyId = ''
  try {
    const validCode = validateAuthCode(code);
    const validRealmId = validateRealmId(realmId);
    const parsedState = validateState(state);

    const { companyId: extractedCompanyId } = parsedState;
    companyId = extractedCompanyId;

    // VALIDATE BUSINESS CONSTRAINTS FIRST - before exchanging token
    await validateIntegrationConstraints(validRealmId, companyId);

    // Exchange token ONLY after validation passes
    const tokenData = await exchangeCodeForToken(validCode);

    // Save token - validation already passed
    await saveToken(tokenData, validRealmId, state);

    return {
      success: true,
      companyId,
      message: 'Connected successfully to QuickBooks.',
    };
  } catch (error) {
    // Ensure connection status is set to DISCONNECTED on any failure
    if (companyId) {
      try {
        await prisma.company.update({
          where: { id: parseInt(companyId) },
          data: {
            qboConnectionStatus: 'DISCONNECTED',
            updatedAt: new Date()
          },
        });
      } catch (updateError) {
        console.error(`Failed to update connection status to DISCONNECTED for company ${companyId}:`, updateError.message);
      }
    }

    // Handle validation constraint errors specifically
    if (
      error.name === 'REALM_ID_CONFLICT' ||
      error.name === 'COMPANY_ID_CONFLICT'
    ) {
      // Extract companyId from state for error context if not already extracted
      if (!companyId) {
        try {
          const parsedState = JSON.parse(state);
          companyId = parsedState.companyId || '';
        } catch {}
      }

      // Create proper error object with companyId
      const constraintError = new Error(error.cause || error.message);
      constraintError.name = error.name;
      constraintError.cause = error.cause || error.message;
      constraintError.companyId = companyId;
      throw constraintError;
    }

    // Handle other errors
    if (!companyId) {
      try {
        const parsedState = JSON.parse(state);
        companyId = parsedState.companyId || '';
      } catch {}
    }

    const finalError = new Error(error.message || 'Callback processing failed');
    finalError.name = error.name || 'CallbackError';
    finalError.cause =
      error.cause || error.message || 'Callback processing failed';
    finalError.companyId = companyId;
    throw finalError;
  }
};

const saveToken = async (tokenData, realmId, state) => {
  try {
    const validRealmId = validateRealmId(realmId);
    const parsedState = validateState(state);
    const { companyId } = parsedState;
    const validCompanyId = validateCompanyId(companyId);

    // Use ensureCompanyExists for company existence check
    await ensureCompanyExists(validCompanyId);

    // Validate token data
    if (!tokenData.access_token || !tokenData.refresh_token) {
      throw new Error('Invalid token data provided');
    }

    const tokenExpiry = new Date(
      Date.now() + (tokenData.expires_in || 3600) * 1000,
    );
    const currentTime = new Date();

    // Save tokens and connection status first
    await prisma.company.update({
      where: { id: validCompanyId },
      data: {
        qboRealmID: validRealmId,
        qboAccessToken: tokenData.access_token,
        qboRefreshToken: tokenData.refresh_token,
        qboTokenExpiryAt: tokenExpiry,
        qboAccessTokenCreatedAt: currentTime,
        qboConnectionStatus: 'CONNECTED',
        updatedAt: currentTime,
      },
    });

    // Fetch QBO company info and update qboCompanyName
    try {
      const baseUrl = getQboApiBaseUrl();
      const url = `${baseUrl}/v3/company/${validRealmId}/companyinfo/${validRealmId}`;

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${tokenData.access_token}`,
          Accept: 'application/json',
        },
      });

      const companyInfo =
        response.data?.CompanyInfo || response.data?.CompanyInfo?.CompanyInfo;
      const qboCompanyName = companyInfo?.CompanyName || null;

      if (qboCompanyName) {
        await prisma.company.update({
          where: { id: validCompanyId },
          data: { qboCompanyName },
        });
      }
    } catch (err) {
      // Log but do not block connection if QBO company info fetch fails
      console.error(
        'Failed to fetch QBO company name:',
        err?.response?.data || err.message,
      );
    }
  } catch (error) {
    throw new Error(`Failed to save token: ${error.message}`);
  }
};

const revokeToken = async (accessToken) => {
  try {
    if (!accessToken || typeof accessToken !== 'string') {
      throw new Error('Valid access token is required');
    }

    const revokeUrl = getQboRevokeUrl();

    await axios.post(
      revokeUrl,
      qs.stringify({
        token: accessToken,
      }),
      {
        headers: {
          Authorization:
            'Basic ' +
            Buffer.from(`${clientId}:${clientSecret}`).toString('base64'),
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    if (error.response?.status === 400) {
      throw new Error('Invalid or already revoked token');
    }
    if (error.response?.status === 401) {
      throw new Error('Authentication failed while revoking token');
    }
    throw new Error(`Token revocation failed: ${error.message}`);
  }
};

const getIntegrationStatus = async (companyId) => {
  try {
    const validCompanyId = validateCompanyId(companyId);

    // Use ensureCompanyExists for company existence check
    const company = await ensureCompanyExists(validCompanyId);

    return {
      success: true,
      qboRealmID: company.qboRealmID,
      connectionStatus: company.qboConnectionStatus,
      tokenExpiresAt: company.qboTokenExpiryAt,
      lastRefreshed: company.updatedAt,
    };
  } catch (error) {
    if (error.message === 'Company not found.') {
      return {
        success: false,
        message: 'Not connected to QuickBooks.',
      };
    }
    throw new Error(`Failed to get integration status: ${error.message}`);
  }
};

const callQuickBooksApi = async (companyId, endpoint) => {
  try {
    const validCompanyId = validateCompanyId(companyId);

    if (!endpoint || typeof endpoint !== 'string') {
      throw new Error('Valid API endpoint is required');
    }

    // Get company to retrieve realmId
    const company = await ensureCompanyExists(validCompanyId);
    if (!company.qboRealmID) {
      throw new Error('Company is not connected to QuickBooks');
    }

    const accessToken = await getValidAccessToken(validCompanyId);
    const baseUrl = getQboApiBaseUrl();
    const url = `${baseUrl}/v3/company/${company.qboRealmID}${endpoint}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: 'application/json',
      },
    });

    return response.data;
  } catch (error) {
    if (error.response?.status === 401) {
      throw new Error('Authentication failed - token may be expired');
    }
    if (error.response?.status === 403) {
      throw new Error('Access forbidden - insufficient permissions');
    }
    if (error.response?.status === 404) {
      throw new Error('QuickBooks API endpoint not found');
    }
    throw new Error(`QuickBooks API call failed: ${error.message}`);
  }
};

const disconnectIntegration = async (companyId) => {
  try {
    const validCompanyId = validateCompanyId(companyId);
    const company = await ensureCompanyExists(validCompanyId);

    await prisma.company.update({
      where: { id: validCompanyId },
      data: {
        qboAccessToken: null,
        qboRefreshToken: '',
        qboTokenExpiryAt: null,
        qboConnectionStatus: 'DISCONNECTED',
        updatedAt: new Date(),
      },
    });
  } catch (error) {
    throw new Error(`Failed to disconnect integration: ${error.message}`);
  }
};

const getValidAccessToken = async (companyId) => {
  // Use ensureCompanyExists for company existence check
  await ensureCompanyExists(companyId);
  const company = await prisma.company.findFirst({
    where: { id: parseInt(companyId), qboConnectionStatus: 'CONNECTED' },
  });

  if (!company) {
    throw {
      statusCode: 404,
      message: 'No active QuickBooks connection found.',
    };
  }

  const isExpired =
    !company.qboTokenExpiryAt ||
    new Date() >= new Date(company.qboTokenExpiryAt);

  if (!isExpired) {
    return company.qboAccessToken;
  }

  // Refresh token
  const refreshResponse = await axios.post(
    tokenUrl,
    qs.stringify({
      grant_type: 'refresh_token',
      refresh_token: company.qboRefreshToken,
    }),
    {
      headers: {
        Authorization:
          'Basic ' +
          Buffer.from(`${clientId}:${clientSecret}`).toString('base64'),
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    },
  );

  const refreshedToken = refreshResponse.data;

  await prisma.company.update({
    where: { id: company.id },
    data: {
      qboAccessToken: refreshedToken.access_token,
      qboRefreshToken: refreshedToken.refresh_token,
      qboTokenExpiryAt: new Date(Date.now() + refreshedToken.expires_in * 1000),
      qboAccessTokenCreatedAt: new Date(),
      updatedAt: new Date(),
    },
  });

  return refreshedToken.access_token;
};

export const ensureCompanyExists = async (companyId) => {
  const company = await prisma.company.findFirst({
    where: { id: Number(companyId) },
  });
  if (!company) {
    throw new Error('Company not found.');
  }
  return company;
};

const qboService = {
  getAuthorizationUrl,
  getValidAccessToken,
  exchangeCodeForToken,
  processCallback,
  getIntegrationStatus,
  callQuickBooksApi,
  disconnectIntegration,
  saveToken,
  revokeToken,
  ensureCompanyExists,
};

export default qboService;
